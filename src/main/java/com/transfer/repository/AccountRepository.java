package com.transfer.repository;

import com.transfer.model.Account;
import com.transfer.model.AccountType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface AccountRepository extends JpaRepository<Account, Long> {
    
    Optional<Account> findByAccountId(String accountId);
    
    List<Account> findByCustomerId(String customerId);
    
    List<Account> findByCustomerIdAndAccountType(String customerId, AccountType accountType);
    
    boolean existsByAccountId(String accountId);
    
    @Query("SELECT a FROM Account a WHERE a.customerId = :customerId ORDER BY a.createdAt DESC")
    List<Account> findByCustomerIdOrderByCreatedAtDesc(@Param("customerId") String customerId);
    
    @Query("SELECT COUNT(a) FROM Account a WHERE a.customerId = :customerId")
    long countByCustomerId(@Param("customerId") String customerId);
    
    @Query("SELECT a FROM Account a WHERE a.customerId = :customerId AND a.accountType = :accountType ORDER BY a.createdAt DESC")
    List<Account> findByCustomerIdAndAccountTypeOrderByCreatedAtDesc(
            @Param("customerId") String customerId, 
            @Param("accountType") AccountType accountType);
}
