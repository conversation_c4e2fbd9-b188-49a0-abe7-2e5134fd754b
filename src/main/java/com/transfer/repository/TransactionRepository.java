package com.transfer.repository;

import com.transfer.model.Transaction;
import com.transfer.model.TransactionStatus;
import com.transfer.model.TransactionType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface TransactionRepository extends JpaRepository<Transaction, Long> {
    
    Optional<Transaction> findByTransactionId(String transactionId);
    
    List<Transaction> findByCustomerId(String customerId);
    
    List<Transaction> findByCustomerIdOrderByCreatedAtDesc(String customerId);
    
    List<Transaction> findByCustomerIdAndTransactionTypeOrderByCreatedAtDesc(
            String customerId, TransactionType transactionType);
    
    List<Transaction> findByCustomerIdAndStatusOrderByCreatedAtDesc(
            String customerId, TransactionStatus status);
    
    @Query("SELECT t FROM Transaction t WHERE t.customerId = :customerId AND " +
           "(t.fromAccountId = :accountId OR t.toAccountId = :accountId) " +
           "ORDER BY t.createdAt DESC")
    List<Transaction> findByCustomerIdAndAccountIdOrderByCreatedAtDesc(
            @Param("customerId") String customerId, 
            @Param("accountId") String accountId);
    
    @Query("SELECT t FROM Transaction t WHERE t.customerId = :customerId AND " +
           "t.createdAt BETWEEN :startDate AND :endDate " +
           "ORDER BY t.createdAt DESC")
    List<Transaction> findByCustomerIdAndDateRangeOrderByCreatedAtDesc(
            @Param("customerId") String customerId,
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate);
    
    List<Transaction> findByRecurringPaymentId(String recurringPaymentId);
    
    @Query("SELECT COUNT(t) FROM Transaction t WHERE t.customerId = :customerId AND t.status = :status")
    long countByCustomerIdAndStatus(@Param("customerId") String customerId, @Param("status") TransactionStatus status);
}
