package com.transfer.repository;

import com.transfer.model.RecurringPayment;
import com.transfer.model.RecurringPaymentStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@Repository
public interface RecurringPaymentRepository extends JpaRepository<RecurringPayment, Long> {
    
    Optional<RecurringPayment> findByRecurringPaymentId(String recurringPaymentId);
    
    List<RecurringPayment> findByCustomerId(String customerId);
    
    List<RecurringPayment> findByCustomerIdOrderByCreatedAtDesc(String customerId);
    
    List<RecurringPayment> findByCustomerIdAndStatusOrderByCreatedAtDesc(
            String customerId, RecurringPaymentStatus status);
    
    @Query("SELECT rp FROM RecurringPayment rp WHERE rp.status = :status AND rp.nextPaymentDate <= :date")
    List<RecurringPayment> findDuePayments(
            @Param("status") RecurringPaymentStatus status, 
            @Param("date") LocalDate date);
    
    @Query("SELECT rp FROM RecurringPayment rp WHERE rp.customerId = :customerId AND " +
           "(rp.fromAccountId = :accountId OR rp.toAccountId = :accountId) " +
           "ORDER BY rp.createdAt DESC")
    List<RecurringPayment> findByCustomerIdAndAccountIdOrderByCreatedAtDesc(
            @Param("customerId") String customerId, 
            @Param("accountId") String accountId);
    
    @Query("SELECT COUNT(rp) FROM RecurringPayment rp WHERE rp.customerId = :customerId AND rp.status = :status")
    long countByCustomerIdAndStatus(@Param("customerId") String customerId, @Param("status") RecurringPaymentStatus status);
}
