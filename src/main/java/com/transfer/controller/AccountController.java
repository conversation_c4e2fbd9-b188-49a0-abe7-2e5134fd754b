package com.transfer.controller;

import com.transfer.model.Account;
import com.transfer.model.AccountType;
import com.transfer.service.AccountService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/api/accounts")
@CrossOrigin(origins = "*")
public class AccountController {
    
    private static final Logger logger = LoggerFactory.getLogger(AccountController.class);
    
    @Autowired
    private AccountService accountService;
    
    @PostMapping
    public ResponseEntity<?> createAccount(@RequestBody Map<String, Object> request) {
        try {
            logger.info("Creating account request: {}", request);
            
            String accountName = (String) request.get("accountName");
            String accountTypeStr = (String) request.get("accountType");
            BigDecimal initialDeposit = new BigDecimal(request.get("initialDeposit").toString());
            String customerId = (String) request.get("customerId");
            
            AccountType accountType = AccountType.valueOf(accountTypeStr.toUpperCase());
            
            Account account = accountService.createAccount(accountName, accountType, initialDeposit, customerId);
            
            logger.info("Account created successfully: {}", account.getAccountId());
            return ResponseEntity.ok(account);
            
        } catch (Exception e) {
            logger.error("Error creating account: {}", e.getMessage(), e);
            return ResponseEntity.badRequest().body(Map.of("error", e.getMessage()));
        }
    }
    
    @GetMapping("/{accountId}")
    public ResponseEntity<?> getAccount(@PathVariable String accountId) {
        try {
            logger.debug("Getting account: {}", accountId);
            
            Optional<Account> account = accountService.getAccountById(accountId);
            if (account.isPresent()) {
                return ResponseEntity.ok(account.get());
            } else {
                return ResponseEntity.notFound().build();
            }
            
        } catch (Exception e) {
            logger.error("Error getting account: {}", e.getMessage(), e);
            return ResponseEntity.badRequest().body(Map.of("error", e.getMessage()));
        }
    }
    
    @GetMapping("/customer/{customerId}")
    public ResponseEntity<?> getAccountsByCustomer(@PathVariable String customerId) {
        try {
            logger.debug("Getting accounts for customer: {}", customerId);
            
            List<Account> accounts = accountService.getAccountsByCustomerId(customerId);
            return ResponseEntity.ok(accounts);
            
        } catch (Exception e) {
            logger.error("Error getting accounts for customer: {}", e.getMessage(), e);
            return ResponseEntity.badRequest().body(Map.of("error", e.getMessage()));
        }
    }
    
    @GetMapping("/customer/{customerId}/type/{accountType}")
    public ResponseEntity<?> getAccountsByCustomerAndType(@PathVariable String customerId, 
                                                         @PathVariable String accountType) {
        try {
            logger.debug("Getting {} accounts for customer: {}", accountType, customerId);
            
            AccountType type = AccountType.valueOf(accountType.toUpperCase());
            List<Account> accounts = accountService.getAccountsByCustomerIdAndType(customerId, type);
            return ResponseEntity.ok(accounts);
            
        } catch (Exception e) {
            logger.error("Error getting accounts by type: {}", e.getMessage(), e);
            return ResponseEntity.badRequest().body(Map.of("error", e.getMessage()));
        }
    }
    
    @PutMapping("/{accountId}/balance")
    public ResponseEntity<?> updateAccountBalance(@PathVariable String accountId, 
                                                 @RequestBody Map<String, Object> request) {
        try {
            logger.info("Updating balance for account: {}", accountId);
            
            BigDecimal newBalance = new BigDecimal(request.get("balance").toString());
            Account account = accountService.updateAccountBalance(accountId, newBalance);
            
            logger.info("Account balance updated successfully: {}", accountId);
            return ResponseEntity.ok(account);
            
        } catch (Exception e) {
            logger.error("Error updating account balance: {}", e.getMessage(), e);
            return ResponseEntity.badRequest().body(Map.of("error", e.getMessage()));
        }
    }
    
    @GetMapping("/{accountId}/balance")
    public ResponseEntity<?> getAccountBalance(@PathVariable String accountId) {
        try {
            logger.debug("Getting balance for account: {}", accountId);
            
            Optional<Account> account = accountService.getAccountById(accountId);
            if (account.isPresent()) {
                return ResponseEntity.ok(Map.of(
                    "accountId", accountId,
                    "balance", account.get().getBalance(),
                    "accountName", account.get().getAccountName(),
                    "accountType", account.get().getAccountType()
                ));
            } else {
                return ResponseEntity.notFound().build();
            }
            
        } catch (Exception e) {
            logger.error("Error getting account balance: {}", e.getMessage(), e);
            return ResponseEntity.badRequest().body(Map.of("error", e.getMessage()));
        }
    }
    
    @GetMapping("/{accountId}/sufficient-funds/{amount}")
    public ResponseEntity<?> checkSufficientFunds(@PathVariable String accountId, 
                                                  @PathVariable BigDecimal amount) {
        try {
            logger.debug("Checking sufficient funds for account: {}, amount: {}", accountId, amount);
            
            boolean hasFunds = accountService.hasSufficientFunds(accountId, amount);
            return ResponseEntity.ok(Map.of(
                "accountId", accountId,
                "amount", amount,
                "hasSufficientFunds", hasFunds
            ));
            
        } catch (Exception e) {
            logger.error("Error checking sufficient funds: {}", e.getMessage(), e);
            return ResponseEntity.badRequest().body(Map.of("error", e.getMessage()));
        }
    }
    
    @GetMapping
    public ResponseEntity<?> getAllAccounts() {
        try {
            logger.debug("Getting all accounts");
            
            List<Account> accounts = accountService.getAllAccounts();
            return ResponseEntity.ok(accounts);
            
        } catch (Exception e) {
            logger.error("Error getting all accounts: {}", e.getMessage(), e);
            return ResponseEntity.badRequest().body(Map.of("error", e.getMessage()));
        }
    }
    
    @DeleteMapping("/{accountId}")
    public ResponseEntity<?> deleteAccount(@PathVariable String accountId) {
        try {
            logger.info("Deleting account: {}", accountId);
            
            accountService.deleteAccount(accountId);
            
            logger.info("Account deleted successfully: {}", accountId);
            return ResponseEntity.ok(Map.of("message", "Account deleted successfully"));
            
        } catch (Exception e) {
            logger.error("Error deleting account: {}", e.getMessage(), e);
            return ResponseEntity.badRequest().body(Map.of("error", e.getMessage()));
        }
    }
}
