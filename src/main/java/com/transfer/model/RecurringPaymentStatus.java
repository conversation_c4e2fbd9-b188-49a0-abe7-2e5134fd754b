package com.transfer.model;

public enum RecurringPaymentStatus {
    ACTIVE("Active"),
    PAUSED("Paused"),
    CANCELLED("Cancelled"),
    COMPLETED("Completed");
    
    private final String displayName;
    
    RecurringPaymentStatus(String displayName) {
        this.displayName = displayName;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    @Override
    public String toString() {
        return displayName;
    }
}
