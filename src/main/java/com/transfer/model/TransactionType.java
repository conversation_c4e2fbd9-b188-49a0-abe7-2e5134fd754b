package com.transfer.model;

public enum TransactionType {
    IMMEDIATE("Immediate Transfer"),
    RECURRING("Recurring Transfer");
    
    private final String displayName;
    
    TransactionType(String displayName) {
        this.displayName = displayName;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    @Override
    public String toString() {
        return displayName;
    }
}
