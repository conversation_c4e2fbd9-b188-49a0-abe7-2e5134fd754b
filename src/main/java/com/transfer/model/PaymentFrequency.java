package com.transfer.model;

import java.time.LocalDate;

public enum PaymentFrequency {
    WEEKLY("Weekly", 7),
    BI_WEEKLY("Bi-Weekly", 14),
    MONTHLY("Monthly", 30);
    
    private final String displayName;
    private final int days;
    
    PaymentFrequency(String displayName, int days) {
        this.displayName = displayName;
        this.days = days;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public int getDays() {
        return days;
    }
    
    public LocalDate calculateNextDate(LocalDate currentDate) {
        switch (this) {
            case WEEKLY:
                return currentDate.plusWeeks(1);
            case BI_WEEKLY:
                return currentDate.plusWeeks(2);
            case MONTHLY:
                return currentDate.plusMonths(1);
            default:
                throw new IllegalArgumentException("Unknown frequency: " + this);
        }
    }
    
    @Override
    public String toString() {
        return displayName;
    }
}
