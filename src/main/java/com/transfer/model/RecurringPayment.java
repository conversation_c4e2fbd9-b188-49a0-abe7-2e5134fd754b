package com.transfer.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Objects;

@Entity
@Table(name = "recurring_payments")
public class RecurringPayment {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @NotBlank(message = "Recurring payment ID is required")
    @Column(name = "recurring_payment_id", unique = true, nullable = false)
    private String recurringPaymentId;
    
    @NotBlank(message = "From account ID is required")
    @Column(name = "from_account_id", nullable = false)
    private String fromAccountId;
    
    @NotBlank(message = "To account ID is required")
    @Column(name = "to_account_id", nullable = false)
    private String toAccountId;
    
    @NotNull(message = "Amount is required")
    @DecimalMin(value = "0.01", message = "Amount must be greater than 0")
    @Column(name = "amount", nullable = false, precision = 19, scale = 2)
    private BigDecimal amount;
    
    @NotNull(message = "Frequency is required")
    @Enumerated(EnumType.STRING)
    @Column(name = "frequency", nullable = false)
    private PaymentFrequency frequency;
    
    @NotNull(message = "Start date is required")
    @Column(name = "start_date", nullable = false)
    private LocalDate startDate;
    
    @Column(name = "end_date")
    private LocalDate endDate;
    
    @NotNull(message = "Next payment date is required")
    @Column(name = "next_payment_date", nullable = false)
    private LocalDate nextPaymentDate;
    
    @NotNull(message = "Status is required")
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private RecurringPaymentStatus status;
    
    @Column(name = "description")
    private String description;
    
    @NotBlank(message = "Customer ID is required")
    @Column(name = "customer_id", nullable = false)
    private String customerId;
    
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @Column(name = "last_executed_at")
    private LocalDateTime lastExecutedAt;
    
    // Constructors
    public RecurringPayment() {
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
        this.status = RecurringPaymentStatus.ACTIVE;
    }
    
    public RecurringPayment(String recurringPaymentId, String fromAccountId, String toAccountId,
                           BigDecimal amount, PaymentFrequency frequency, LocalDate startDate,
                           String customerId) {
        this();
        this.recurringPaymentId = recurringPaymentId;
        this.fromAccountId = fromAccountId;
        this.toAccountId = toAccountId;
        this.amount = amount;
        this.frequency = frequency;
        this.startDate = startDate;
        this.nextPaymentDate = startDate;
        this.customerId = customerId;
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getRecurringPaymentId() {
        return recurringPaymentId;
    }
    
    public void setRecurringPaymentId(String recurringPaymentId) {
        this.recurringPaymentId = recurringPaymentId;
    }
    
    public String getFromAccountId() {
        return fromAccountId;
    }
    
    public void setFromAccountId(String fromAccountId) {
        this.fromAccountId = fromAccountId;
        this.updatedAt = LocalDateTime.now();
    }
    
    public String getToAccountId() {
        return toAccountId;
    }
    
    public void setToAccountId(String toAccountId) {
        this.toAccountId = toAccountId;
        this.updatedAt = LocalDateTime.now();
    }
    
    public BigDecimal getAmount() {
        return amount;
    }
    
    public void setAmount(BigDecimal amount) {
        this.amount = amount;
        this.updatedAt = LocalDateTime.now();
    }
    
    public PaymentFrequency getFrequency() {
        return frequency;
    }
    
    public void setFrequency(PaymentFrequency frequency) {
        this.frequency = frequency;
        this.updatedAt = LocalDateTime.now();
    }
    
    public LocalDate getStartDate() {
        return startDate;
    }
    
    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
        this.updatedAt = LocalDateTime.now();
    }
    
    public LocalDate getEndDate() {
        return endDate;
    }
    
    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
        this.updatedAt = LocalDateTime.now();
    }
    
    public LocalDate getNextPaymentDate() {
        return nextPaymentDate;
    }
    
    public void setNextPaymentDate(LocalDate nextPaymentDate) {
        this.nextPaymentDate = nextPaymentDate;
        this.updatedAt = LocalDateTime.now();
    }
    
    public RecurringPaymentStatus getStatus() {
        return status;
    }
    
    public void setStatus(RecurringPaymentStatus status) {
        this.status = status;
        this.updatedAt = LocalDateTime.now();
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
        this.updatedAt = LocalDateTime.now();
    }
    
    public String getCustomerId() {
        return customerId;
    }
    
    public void setCustomerId(String customerId) {
        this.customerId = customerId;
        this.updatedAt = LocalDateTime.now();
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    public LocalDateTime getLastExecutedAt() {
        return lastExecutedAt;
    }
    
    public void setLastExecutedAt(LocalDateTime lastExecutedAt) {
        this.lastExecutedAt = lastExecutedAt;
        this.updatedAt = LocalDateTime.now();
    }
    
    // Business methods
    public void updateNextPaymentDate() {
        this.nextPaymentDate = frequency.calculateNextDate(this.nextPaymentDate);
        this.updatedAt = LocalDateTime.now();
    }
    
    public boolean isActive() {
        return status == RecurringPaymentStatus.ACTIVE;
    }
    
    public boolean isDue() {
        return isActive() && !nextPaymentDate.isAfter(LocalDate.now());
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        RecurringPayment that = (RecurringPayment) o;
        return Objects.equals(recurringPaymentId, that.recurringPaymentId);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(recurringPaymentId);
    }
    
    @Override
    public String toString() {
        return "RecurringPayment{" +
                "id=" + id +
                ", recurringPaymentId='" + recurringPaymentId + '\'' +
                ", fromAccountId='" + fromAccountId + '\'' +
                ", toAccountId='" + toAccountId + '\'' +
                ", amount=" + amount +
                ", frequency=" + frequency +
                ", startDate=" + startDate +
                ", endDate=" + endDate +
                ", nextPaymentDate=" + nextPaymentDate +
                ", status=" + status +
                ", description='" + description + '\'' +
                ", customerId='" + customerId + '\'' +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                ", lastExecutedAt=" + lastExecutedAt +
                '}';
    }
}
