package com.transfer.service;

import com.transfer.model.PaymentFrequency;
import com.transfer.model.RecurringPayment;
import com.transfer.model.RecurringPaymentStatus;
import com.transfer.repository.RecurringPaymentRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Service
@Transactional
public class RecurringPaymentService {
    
    private static final Logger logger = LoggerFactory.getLogger(RecurringPaymentService.class);
    
    @Autowired
    private RecurringPaymentRepository recurringPaymentRepository;
    
    @Autowired
    private TransactionService transactionService;
    
    @Autowired
    private AccountService accountService;
    
    public RecurringPayment createRecurringPayment(String fromAccountId, String toAccountId,
                                                 BigDecimal amount, PaymentFrequency frequency,
                                                 LocalDate startDate, String customerId, String description) {
        logger.info("Creating recurring payment from {} to {} for amount {} with frequency {} starting {}", 
                   fromAccountId, toAccountId, amount, frequency, startDate);
        
        // Validation
        validateRecurringPaymentRequest(fromAccountId, toAccountId, amount, frequency, startDate, customerId);
        
        String recurringPaymentId = generateRecurringPaymentId();
        
        RecurringPayment recurringPayment = new RecurringPayment(recurringPaymentId, fromAccountId, 
                                                               toAccountId, amount, frequency, startDate, customerId);
        recurringPayment.setDescription(description);
        
        RecurringPayment savedPayment = recurringPaymentRepository.save(recurringPayment);
        
        logger.info("Recurring payment created successfully: {}", recurringPaymentId);
        return savedPayment;
    }
    
    public Optional<RecurringPayment> getRecurringPaymentById(String recurringPaymentId) {
        logger.debug("Retrieving recurring payment by ID: {}", recurringPaymentId);
        return recurringPaymentRepository.findByRecurringPaymentId(recurringPaymentId);
    }
    
    public List<RecurringPayment> getRecurringPaymentsByCustomerId(String customerId) {
        logger.debug("Retrieving recurring payments for customer: {}", customerId);
        return recurringPaymentRepository.findByCustomerIdOrderByCreatedAtDesc(customerId);
    }
    
    public List<RecurringPayment> getRecurringPaymentsByCustomerIdAndStatus(String customerId, 
                                                                           RecurringPaymentStatus status) {
        logger.debug("Retrieving {} recurring payments for customer: {}", status, customerId);
        return recurringPaymentRepository.findByCustomerIdAndStatusOrderByCreatedAtDesc(customerId, status);
    }
    
    public List<RecurringPayment> getRecurringPaymentsByAccountId(String customerId, String accountId) {
        logger.debug("Retrieving recurring payments for customer: {} and account: {}", customerId, accountId);
        return recurringPaymentRepository.findByCustomerIdAndAccountIdOrderByCreatedAtDesc(customerId, accountId);
    }
    
    public RecurringPayment updateRecurringPaymentStatus(String recurringPaymentId, RecurringPaymentStatus status) {
        logger.info("Updating recurring payment {} status to {}", recurringPaymentId, status);
        
        RecurringPayment recurringPayment = recurringPaymentRepository.findByRecurringPaymentId(recurringPaymentId)
                .orElseThrow(() -> new IllegalArgumentException("Recurring payment not found: " + recurringPaymentId));
        
        recurringPayment.setStatus(status);
        RecurringPayment updatedPayment = recurringPaymentRepository.save(recurringPayment);
        
        logger.info("Recurring payment status updated successfully: {}", recurringPaymentId);
        return updatedPayment;
    }
    
    public RecurringPayment pauseRecurringPayment(String recurringPaymentId) {
        return updateRecurringPaymentStatus(recurringPaymentId, RecurringPaymentStatus.PAUSED);
    }
    
    public RecurringPayment resumeRecurringPayment(String recurringPaymentId) {
        return updateRecurringPaymentStatus(recurringPaymentId, RecurringPaymentStatus.ACTIVE);
    }
    
    public RecurringPayment cancelRecurringPayment(String recurringPaymentId) {
        return updateRecurringPaymentStatus(recurringPaymentId, RecurringPaymentStatus.CANCELLED);
    }
    
    @Scheduled(fixedRate = 60000) // Run every minute
    public void processRecurringPayments() {
        logger.debug("Processing due recurring payments");
        
        List<RecurringPayment> duePayments = recurringPaymentRepository.findDuePayments(
                RecurringPaymentStatus.ACTIVE, LocalDate.now());
        
        logger.info("Found {} due recurring payments", duePayments.size());
        
        for (RecurringPayment payment : duePayments) {
            try {
                processRecurringPayment(payment);
            } catch (Exception e) {
                logger.error("Failed to process recurring payment: {}", payment.getRecurringPaymentId(), e);
            }
        }
    }
    
    private void processRecurringPayment(RecurringPayment recurringPayment) {
        logger.info("Processing recurring payment: {}", recurringPayment.getRecurringPaymentId());
        
        try {
            // Check if source account has sufficient funds
            if (!accountService.hasSufficientFunds(recurringPayment.getFromAccountId(), recurringPayment.getAmount())) {
                logger.warn("Insufficient funds for recurring payment: {}", recurringPayment.getRecurringPaymentId());
                return; // Skip this payment, will try again next time
            }
            
            // Execute the transaction
            transactionService.createRecurringTransaction(
                    recurringPayment.getFromAccountId(),
                    recurringPayment.getToAccountId(),
                    recurringPayment.getAmount(),
                    recurringPayment.getCustomerId(),
                    recurringPayment.getRecurringPaymentId(),
                    "Recurring payment: " + recurringPayment.getDescription()
            );
            
            // Update next payment date
            recurringPayment.updateNextPaymentDate();
            recurringPayment.setLastExecutedAt(LocalDateTime.now());
            
            // Check if payment should be completed (if end date is reached)
            if (recurringPayment.getEndDate() != null && 
                recurringPayment.getNextPaymentDate().isAfter(recurringPayment.getEndDate())) {
                recurringPayment.setStatus(RecurringPaymentStatus.COMPLETED);
            }
            
            recurringPaymentRepository.save(recurringPayment);
            
            logger.info("Recurring payment processed successfully: {}", recurringPayment.getRecurringPaymentId());
            
        } catch (Exception e) {
            logger.error("Failed to process recurring payment: {}", recurringPayment.getRecurringPaymentId(), e);
            throw e;
        }
    }
    
    public long getRecurringPaymentCountByCustomerIdAndStatus(String customerId, RecurringPaymentStatus status) {
        return recurringPaymentRepository.countByCustomerIdAndStatus(customerId, status);
    }
    
    private void validateRecurringPaymentRequest(String fromAccountId, String toAccountId,
                                               BigDecimal amount, PaymentFrequency frequency,
                                               LocalDate startDate, String customerId) {
        logger.debug("Validating recurring payment request from {} to {} for amount {}", 
                    fromAccountId, toAccountId, amount);
        
        // Basic validations
        if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("Payment amount must be greater than zero");
        }
        
        if (fromAccountId == null || fromAccountId.trim().isEmpty()) {
            throw new IllegalArgumentException("From account ID is required");
        }
        
        if (toAccountId == null || toAccountId.trim().isEmpty()) {
            throw new IllegalArgumentException("To account ID is required");
        }
        
        if (fromAccountId.equals(toAccountId)) {
            throw new IllegalArgumentException("Cannot schedule payment to the same account");
        }
        
        if (frequency == null) {
            throw new IllegalArgumentException("Payment frequency is required");
        }
        
        if (startDate == null) {
            throw new IllegalArgumentException("Start date is required");
        }
        
        if (startDate.isBefore(LocalDate.now())) {
            throw new IllegalArgumentException("Start date cannot be in the past");
        }
        
        // Check if accounts exist
        if (!accountService.hasAccount(fromAccountId)) {
            throw new IllegalArgumentException("From account not found: " + fromAccountId);
        }
        
        if (!accountService.hasAccount(toAccountId)) {
            throw new IllegalArgumentException("To account not found: " + toAccountId);
        }
        
        logger.debug("Recurring payment request validation passed");
    }
    
    private String generateRecurringPaymentId() {
        return "REC-" + UUID.randomUUID().toString().replace("-", "").substring(0, 12).toUpperCase();
    }
    
    public List<RecurringPayment> getAllRecurringPayments() {
        logger.debug("Retrieving all recurring payments");
        return recurringPaymentRepository.findAll();
    }
}
