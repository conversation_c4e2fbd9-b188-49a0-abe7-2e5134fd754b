package com.transfer.service;

import com.transfer.model.Transaction;
import com.transfer.model.TransactionStatus;
import com.transfer.model.TransactionType;
import com.transfer.repository.TransactionRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Service
@Transactional
public class TransactionService {
    
    private static final Logger logger = LoggerFactory.getLogger(TransactionService.class);
    
    @Autowired
    private TransactionRepository transactionRepository;
    
    @Autowired
    private AccountService accountService;
    
    public Transaction executeImmediateTransfer(String fromAccountId, String toAccountId, 
                                              BigDecimal amount, String customerId, String description) {
        logger.info("Executing immediate transfer from {} to {} for amount {} by customer {}", 
                   fromAccountId, toAccountId, amount, customerId);
        
        // Validation
        validateTransferRequest(fromAccountId, toAccountId, amount, customerId);
        
        // Create transaction record
        String transactionId = generateTransactionId();
        Transaction transaction = new Transaction(transactionId, fromAccountId, toAccountId, 
                                                amount, TransactionType.IMMEDIATE, customerId);
        transaction.setDescription(description);
        
        try {
            // Execute the transfer
            accountService.debitAccount(fromAccountId, amount);
            accountService.creditAccount(toAccountId, amount);
            
            // Mark transaction as completed
            transaction.setStatus(TransactionStatus.COMPLETED);
            Transaction savedTransaction = transactionRepository.save(transaction);
            
            logger.info("Immediate transfer completed successfully: {}", transactionId);
            return savedTransaction;
            
        } catch (Exception e) {
            logger.error("Transfer failed: {}", e.getMessage(), e);
            transaction.setStatus(TransactionStatus.FAILED);
            transactionRepository.save(transaction);
            throw new RuntimeException("Transfer failed: " + e.getMessage(), e);
        }
    }
    
    public Transaction createRecurringTransaction(String fromAccountId, String toAccountId, 
                                                BigDecimal amount, String customerId, 
                                                String recurringPaymentId, String description) {
        logger.info("Creating recurring transaction from {} to {} for amount {} by customer {}", 
                   fromAccountId, toAccountId, amount, customerId);
        
        // Validation
        validateTransferRequest(fromAccountId, toAccountId, amount, customerId);
        
        // Create transaction record
        String transactionId = generateTransactionId();
        Transaction transaction = new Transaction(transactionId, fromAccountId, toAccountId, 
                                                amount, TransactionType.RECURRING, customerId);
        transaction.setDescription(description);
        transaction.setRecurringPaymentId(recurringPaymentId);
        
        try {
            // Execute the transfer
            accountService.debitAccount(fromAccountId, amount);
            accountService.creditAccount(toAccountId, amount);
            
            // Mark transaction as completed
            transaction.setStatus(TransactionStatus.COMPLETED);
            Transaction savedTransaction = transactionRepository.save(transaction);
            
            logger.info("Recurring transaction completed successfully: {}", transactionId);
            return savedTransaction;
            
        } catch (Exception e) {
            logger.error("Recurring transaction failed: {}", e.getMessage(), e);
            transaction.setStatus(TransactionStatus.FAILED);
            transactionRepository.save(transaction);
            throw new RuntimeException("Recurring transaction failed: " + e.getMessage(), e);
        }
    }
    
    public Optional<Transaction> getTransactionById(String transactionId) {
        logger.debug("Retrieving transaction by ID: {}", transactionId);
        return transactionRepository.findByTransactionId(transactionId);
    }
    
    public List<Transaction> getTransactionsByCustomerId(String customerId) {
        logger.debug("Retrieving transactions for customer: {}", customerId);
        return transactionRepository.findByCustomerIdOrderByCreatedAtDesc(customerId);
    }
    
    public List<Transaction> getTransactionsByCustomerIdAndType(String customerId, TransactionType transactionType) {
        logger.debug("Retrieving {} transactions for customer: {}", transactionType, customerId);
        return transactionRepository.findByCustomerIdAndTransactionTypeOrderByCreatedAtDesc(customerId, transactionType);
    }
    
    public List<Transaction> getTransactionsByCustomerIdAndStatus(String customerId, TransactionStatus status) {
        logger.debug("Retrieving {} transactions for customer: {}", status, customerId);
        return transactionRepository.findByCustomerIdAndStatusOrderByCreatedAtDesc(customerId, status);
    }
    
    public List<Transaction> getTransactionsByAccountId(String customerId, String accountId) {
        logger.debug("Retrieving transactions for customer: {} and account: {}", customerId, accountId);
        return transactionRepository.findByCustomerIdAndAccountIdOrderByCreatedAtDesc(customerId, accountId);
    }
    
    public List<Transaction> getTransactionsByDateRange(String customerId, LocalDateTime startDate, LocalDateTime endDate) {
        logger.debug("Retrieving transactions for customer: {} between {} and {}", customerId, startDate, endDate);
        return transactionRepository.findByCustomerIdAndDateRangeOrderByCreatedAtDesc(customerId, startDate, endDate);
    }
    
    public List<Transaction> getTransactionsByRecurringPaymentId(String recurringPaymentId) {
        logger.debug("Retrieving transactions for recurring payment: {}", recurringPaymentId);
        return transactionRepository.findByRecurringPaymentId(recurringPaymentId);
    }
    
    public long getTransactionCountByCustomerIdAndStatus(String customerId, TransactionStatus status) {
        return transactionRepository.countByCustomerIdAndStatus(customerId, status);
    }
    
    private void validateTransferRequest(String fromAccountId, String toAccountId, 
                                       BigDecimal amount, String customerId) {
        logger.debug("Validating transfer request from {} to {} for amount {}", 
                    fromAccountId, toAccountId, amount);
        
        // Basic validations
        if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("Transfer amount must be greater than zero");
        }
        
        if (fromAccountId == null || fromAccountId.trim().isEmpty()) {
            throw new IllegalArgumentException("From account ID is required");
        }
        
        if (toAccountId == null || toAccountId.trim().isEmpty()) {
            throw new IllegalArgumentException("To account ID is required");
        }
        
        if (fromAccountId.equals(toAccountId)) {
            throw new IllegalArgumentException("Cannot transfer to the same account");
        }
        
        // Check if accounts exist
        if (!accountService.hasAccount(fromAccountId)) {
            throw new IllegalArgumentException("From account not found: " + fromAccountId);
        }
        
        if (!accountService.hasAccount(toAccountId)) {
            throw new IllegalArgumentException("To account not found: " + toAccountId);
        }
        
        // Check sufficient funds
        if (!accountService.hasSufficientFunds(fromAccountId, amount)) {
            throw new IllegalArgumentException("Insufficient funds in source account");
        }
        
        logger.debug("Transfer request validation passed");
    }
    
    private String generateTransactionId() {
        return "TXN-" + UUID.randomUUID().toString().replace("-", "").substring(0, 12).toUpperCase();
    }
    
    public List<Transaction> getAllTransactions() {
        logger.debug("Retrieving all transactions");
        return transactionRepository.findAll();
    }
}
