package com.transfer.service;

import com.transfer.model.Account;
import com.transfer.model.AccountType;
import com.transfer.repository.AccountRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Service
@Transactional
public class AccountService {
    
    private static final Logger logger = LoggerFactory.getLogger(AccountService.class);
    
    @Autowired
    private AccountRepository accountRepository;
    
    public Account createAccount(String accountName, AccountType accountType, 
                               BigDecimal initialDeposit, String customerId) {
        logger.info("Creating account for customer: {}, type: {}, initial deposit: {}", 
                   customerId, accountType, initialDeposit);
        
        if (initialDeposit.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("Initial deposit cannot be negative");
        }
        
        String accountId = generateAccountId(accountType);
        
        Account account = new Account(accountName, accountId, initialDeposit, accountType, customerId);
        Account savedAccount = accountRepository.save(account);
        
        logger.info("Account created successfully: {}", savedAccount.getAccountId());
        return savedAccount;
    }
    
    public Optional<Account> getAccountById(String accountId) {
        logger.debug("Retrieving account by ID: {}", accountId);
        return accountRepository.findByAccountId(accountId);
    }
    
    public List<Account> getAccountsByCustomerId(String customerId) {
        logger.debug("Retrieving accounts for customer: {}", customerId);
        return accountRepository.findByCustomerIdOrderByCreatedAtDesc(customerId);
    }
    
    public List<Account> getAccountsByCustomerIdAndType(String customerId, AccountType accountType) {
        logger.debug("Retrieving {} accounts for customer: {}", accountType, customerId);
        return accountRepository.findByCustomerIdAndAccountTypeOrderByCreatedAtDesc(customerId, accountType);
    }
    
    public Account updateAccountBalance(String accountId, BigDecimal newBalance) {
        logger.info("Updating balance for account: {} to {}", accountId, newBalance);
        
        Account account = accountRepository.findByAccountId(accountId)
                .orElseThrow(() -> new IllegalArgumentException("Account not found: " + accountId));
        
        if (newBalance.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("Account balance cannot be negative");
        }
        
        account.setBalance(newBalance);
        Account updatedAccount = accountRepository.save(account);
        
        logger.info("Account balance updated successfully for: {}", accountId);
        return updatedAccount;
    }
    
    public boolean hasAccount(String accountId) {
        return accountRepository.existsByAccountId(accountId);
    }
    
    public boolean hasSufficientFunds(String accountId, BigDecimal amount) {
        logger.debug("Checking sufficient funds for account: {}, amount: {}", accountId, amount);
        
        Account account = accountRepository.findByAccountId(accountId)
                .orElseThrow(() -> new IllegalArgumentException("Account not found: " + accountId));
        
        boolean hasFunds = account.hasSufficientFunds(amount);
        logger.debug("Sufficient funds check result: {} for account: {}", hasFunds, accountId);
        
        return hasFunds;
    }
    
    public void debitAccount(String accountId, BigDecimal amount) {
        logger.info("Debiting account: {} with amount: {}", accountId, amount);
        
        Account account = accountRepository.findByAccountId(accountId)
                .orElseThrow(() -> new IllegalArgumentException("Account not found: " + accountId));
        
        account.debit(amount);
        accountRepository.save(account);
        
        logger.info("Account debited successfully: {}, new balance: {}", accountId, account.getBalance());
    }
    
    public void creditAccount(String accountId, BigDecimal amount) {
        logger.info("Crediting account: {} with amount: {}", accountId, amount);
        
        Account account = accountRepository.findByAccountId(accountId)
                .orElseThrow(() -> new IllegalArgumentException("Account not found: " + accountId));
        
        account.credit(amount);
        accountRepository.save(account);
        
        logger.info("Account credited successfully: {}, new balance: {}", accountId, account.getBalance());
    }
    
    public long getAccountCountByCustomerId(String customerId) {
        return accountRepository.countByCustomerId(customerId);
    }
    
    private String generateAccountId(AccountType accountType) {
        String prefix = accountType == AccountType.CHECKING ? "CHK" : "SAV";
        String uuid = UUID.randomUUID().toString().replace("-", "").substring(0, 8).toUpperCase();
        return prefix + "-" + uuid;
    }
    
    public List<Account> getAllAccounts() {
        logger.debug("Retrieving all accounts");
        return accountRepository.findAll();
    }
    
    public void deleteAccount(String accountId) {
        logger.info("Deleting account: {}", accountId);
        
        Account account = accountRepository.findByAccountId(accountId)
                .orElseThrow(() -> new IllegalArgumentException("Account not found: " + accountId));
        
        if (account.getBalance().compareTo(BigDecimal.ZERO) != 0) {
            throw new IllegalStateException("Cannot delete account with non-zero balance");
        }
        
        accountRepository.delete(account);
        logger.info("Account deleted successfully: {}", accountId);
    }
}
