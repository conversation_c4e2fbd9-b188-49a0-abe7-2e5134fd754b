🤖 Starting AI Agents...
Installing dependencies...
Requirement already satisfied: openai>=1.3.0 in ./venv/lib/python3.11/site-packages (from -r requirements.txt (line 1)) (1.86.0)
Requirement already satisfied: mcp>=1.0.0 in ./venv/lib/python3.11/site-packages (from -r requirements.txt (line 2)) (1.9.4)
Requirement already satisfied: pydantic>=2.0.0 in ./venv/lib/python3.11/site-packages (from -r requirements.txt (line 3)) (2.11.7)
Requirement already satisfied: python-dotenv>=1.0.0 in ./venv/lib/python3.11/site-packages (from -r requirements.txt (line 4)) (1.1.0)
Requirement already satisfied: httpx>=0.25.0 in ./venv/lib/python3.11/site-packages (from -r requirements.txt (line 5)) (0.28.1)
Requirement already satisfied: fastapi>=0.104.0 in ./venv/lib/python3.11/site-packages (from -r requirements.txt (line 6)) (0.115.12)
Requirement already satisfied: uvicorn>=0.24.0 in ./venv/lib/python3.11/site-packages (from -r requirements.txt (line 7)) (0.34.3)
Requirement already satisfied: websockets>=12.0 in ./venv/lib/python3.11/site-packages (from -r requirements.txt (line 8)) (15.0.1)
Requirement already satisfied: anyio<5,>=3.5.0 in ./venv/lib/python3.11/site-packages (from openai>=1.3.0->-r requirements.txt (line 1)) (4.9.0)
Requirement already satisfied: distro<2,>=1.7.0 in ./venv/lib/python3.11/site-packages (from openai>=1.3.0->-r requirements.txt (line 1)) (1.9.0)
Requirement already satisfied: jiter<1,>=0.4.0 in ./venv/lib/python3.11/site-packages (from openai>=1.3.0->-r requirements.txt (line 1)) (0.10.0)
Requirement already satisfied: sniffio in ./venv/lib/python3.11/site-packages (from openai>=1.3.0->-r requirements.txt (line 1)) (1.3.1)
Requirement already satisfied: tqdm>4 in ./venv/lib/python3.11/site-packages (from openai>=1.3.0->-r requirements.txt (line 1)) (4.67.1)
Requirement already satisfied: typing-extensions<5,>=4.11 in ./venv/lib/python3.11/site-packages (from openai>=1.3.0->-r requirements.txt (line 1)) (4.14.0)
Requirement already satisfied: httpx-sse>=0.4 in ./venv/lib/python3.11/site-packages (from mcp>=1.0.0->-r requirements.txt (line 2)) (0.4.0)
Requirement already satisfied: pydantic-settings>=2.5.2 in ./venv/lib/python3.11/site-packages (from mcp>=1.0.0->-r requirements.txt (line 2)) (2.9.1)
Requirement already satisfied: python-multipart>=0.0.9 in ./venv/lib/python3.11/site-packages (from mcp>=1.0.0->-r requirements.txt (line 2)) (0.0.20)
Requirement already satisfied: sse-starlette>=1.6.1 in ./venv/lib/python3.11/site-packages (from mcp>=1.0.0->-r requirements.txt (line 2)) (2.3.6)
Requirement already satisfied: starlette>=0.27 in ./venv/lib/python3.11/site-packages (from mcp>=1.0.0->-r requirements.txt (line 2)) (0.46.2)
Requirement already satisfied: annotated-types>=0.6.0 in ./venv/lib/python3.11/site-packages (from pydantic>=2.0.0->-r requirements.txt (line 3)) (0.7.0)
Requirement already satisfied: pydantic-core==2.33.2 in ./venv/lib/python3.11/site-packages (from pydantic>=2.0.0->-r requirements.txt (line 3)) (2.33.2)
Requirement already satisfied: typing-inspection>=0.4.0 in ./venv/lib/python3.11/site-packages (from pydantic>=2.0.0->-r requirements.txt (line 3)) (0.4.1)
Requirement already satisfied: certifi in ./venv/lib/python3.11/site-packages (from httpx>=0.25.0->-r requirements.txt (line 5)) (2025.6.15)
Requirement already satisfied: httpcore==1.* in ./venv/lib/python3.11/site-packages (from httpx>=0.25.0->-r requirements.txt (line 5)) (1.0.9)
Requirement already satisfied: idna in ./venv/lib/python3.11/site-packages (from httpx>=0.25.0->-r requirements.txt (line 5)) (3.10)
Requirement already satisfied: h11>=0.16 in ./venv/lib/python3.11/site-packages (from httpcore==1.*->httpx>=0.25.0->-r requirements.txt (line 5)) (0.16.0)
Requirement already satisfied: click>=7.0 in ./venv/lib/python3.11/site-packages (from uvicorn>=0.24.0->-r requirements.txt (line 7)) (8.2.1)

[notice] A new release of pip is available: 24.0 -> 25.1.1
[notice] To update, run: pip install --upgrade pip
Starting Server Agent (WebSocket server on port 8765)...
Waiting for Server Agent to initialize...
2025-06-15 21:39:47,042 - asyncio - DEBUG - Using selector: KqueueSelector
2025-06-15 21:39:47,066 - __main__ - INFO - Server Agent initialized with OpenAI integration
2025-06-15 21:39:47,066 - __main__ - INFO - Starting Server Agent...
2025-06-15 21:39:47,066 - __main__ - INFO - Starting MCP server...
2025-06-15 21:39:47,068 - __main__ - INFO - MCP server started successfully
2025-06-15 21:39:49,072 - __main__ - INFO - Starting WebSocket server on localhost:8765
2025-06-15 21:39:49,095 - websockets.server - INFO - server listening on [::1]:8765
2025-06-15 21:39:49,095 - websockets.server - INFO - server listening on 127.0.0.1:8765
2025-06-15 21:39:49,095 - __main__ - INFO - WebSocket server started successfully
2025-06-15 21:39:49,095 - __main__ - INFO - Server Agent ready to process requests
✅ Starting Client Agent (HTTP server on port 8766)...

🎉 AI Agents started successfully!
📡 Server Agent (WebSocket): ws://localhost:8765
🌐 Client Agent (HTTP): http://localhost:8766
Server Agent PID: 39134
Client Agent PID: 39143

Press Ctrl+C to stop all agents
2025-06-15 21:39:51,980 - asyncio - DEBUG - Using selector: KqueueSelector
2025-06-15 21:39:51,999 - __main__ - INFO - Client Agent initialized with OpenAI integration
2025-06-15 21:39:51,999 - __main__ - INFO - Starting Client Agent...
2025-06-15 21:39:52,061 - __main__ - INFO - Starting Client Agent HTTP server on port 8766
INFO:     Started server process [39143]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8766 (Press CTRL+C to quit)
2025-06-15 21:39:52,922 - __main__ - INFO - Starting conversation for user: DEFAULT_USER
INFO:     127.0.0.1:56568 - "POST /start_conversation HTTP/1.1" 200 OK
