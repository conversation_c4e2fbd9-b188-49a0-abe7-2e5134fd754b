🤖 Starting AI Agents...
Installing dependencies...
Requirement already satisfied: openai>=1.3.0 in ./venv/lib/python3.11/site-packages (from -r requirements.txt (line 1)) (1.86.0)
Requirement already satisfied: mcp>=1.0.0 in ./venv/lib/python3.11/site-packages (from -r requirements.txt (line 2)) (1.9.4)
Requirement already satisfied: pydantic>=2.0.0 in ./venv/lib/python3.11/site-packages (from -r requirements.txt (line 3)) (2.11.7)
Requirement already satisfied: python-dotenv>=1.0.0 in ./venv/lib/python3.11/site-packages (from -r requirements.txt (line 4)) (1.1.0)
Requirement already satisfied: httpx>=0.25.0 in ./venv/lib/python3.11/site-packages (from -r requirements.txt (line 5)) (0.28.1)
Requirement already satisfied: fastapi>=0.104.0 in ./venv/lib/python3.11/site-packages (from -r requirements.txt (line 6)) (0.115.12)
Requirement already satisfied: uvicorn>=0.24.0 in ./venv/lib/python3.11/site-packages (from -r requirements.txt (line 7)) (0.34.3)
Requirement already satisfied: websockets>=12.0 in ./venv/lib/python3.11/site-packages (from -r requirements.txt (line 8)) (15.0.1)
Requirement already satisfied: anyio<5,>=3.5.0 in ./venv/lib/python3.11/site-packages (from openai>=1.3.0->-r requirements.txt (line 1)) (4.9.0)
Requirement already satisfied: distro<2,>=1.7.0 in ./venv/lib/python3.11/site-packages (from openai>=1.3.0->-r requirements.txt (line 1)) (1.9.0)
Requirement already satisfied: jiter<1,>=0.4.0 in ./venv/lib/python3.11/site-packages (from openai>=1.3.0->-r requirements.txt (line 1)) (0.10.0)
Requirement already satisfied: sniffio in ./venv/lib/python3.11/site-packages (from openai>=1.3.0->-r requirements.txt (line 1)) (1.3.1)
Requirement already satisfied: tqdm>4 in ./venv/lib/python3.11/site-packages (from openai>=1.3.0->-r requirements.txt (line 1)) (4.67.1)
Requirement already satisfied: typing-extensions<5,>=4.11 in ./venv/lib/python3.11/site-packages (from openai>=1.3.0->-r requirements.txt (line 1)) (4.14.0)
Requirement already satisfied: httpx-sse>=0.4 in ./venv/lib/python3.11/site-packages (from mcp>=1.0.0->-r requirements.txt (line 2)) (0.4.0)
Requirement already satisfied: pydantic-settings>=2.5.2 in ./venv/lib/python3.11/site-packages (from mcp>=1.0.0->-r requirements.txt (line 2)) (2.9.1)
Requirement already satisfied: python-multipart>=0.0.9 in ./venv/lib/python3.11/site-packages (from mcp>=1.0.0->-r requirements.txt (line 2)) (0.0.20)
Requirement already satisfied: sse-starlette>=1.6.1 in ./venv/lib/python3.11/site-packages (from mcp>=1.0.0->-r requirements.txt (line 2)) (2.3.6)
Requirement already satisfied: starlette>=0.27 in ./venv/lib/python3.11/site-packages (from mcp>=1.0.0->-r requirements.txt (line 2)) (0.46.2)
Requirement already satisfied: annotated-types>=0.6.0 in ./venv/lib/python3.11/site-packages (from pydantic>=2.0.0->-r requirements.txt (line 3)) (0.7.0)
Requirement already satisfied: pydantic-core==2.33.2 in ./venv/lib/python3.11/site-packages (from pydantic>=2.0.0->-r requirements.txt (line 3)) (2.33.2)
Requirement already satisfied: typing-inspection>=0.4.0 in ./venv/lib/python3.11/site-packages (from pydantic>=2.0.0->-r requirements.txt (line 3)) (0.4.1)
Requirement already satisfied: certifi in ./venv/lib/python3.11/site-packages (from httpx>=0.25.0->-r requirements.txt (line 5)) (2025.6.15)
Requirement already satisfied: httpcore==1.* in ./venv/lib/python3.11/site-packages (from httpx>=0.25.0->-r requirements.txt (line 5)) (1.0.9)
Requirement already satisfied: idna in ./venv/lib/python3.11/site-packages (from httpx>=0.25.0->-r requirements.txt (line 5)) (3.10)
Requirement already satisfied: h11>=0.16 in ./venv/lib/python3.11/site-packages (from httpcore==1.*->httpx>=0.25.0->-r requirements.txt (line 5)) (0.16.0)
Requirement already satisfied: click>=7.0 in ./venv/lib/python3.11/site-packages (from uvicorn>=0.24.0->-r requirements.txt (line 7)) (8.2.1)

[notice] A new release of pip is available: 24.0 -> 25.1.1
[notice] To update, run: pip install --upgrade pip
Starting Server Agent (WebSocket server on port 8765)...
Waiting for Server Agent to initialize...
2025-06-15 21:17:27,951 - asyncio - DEBUG - Using selector: KqueueSelector
2025-06-15 21:17:27,976 - __main__ - INFO - Server Agent initialized with OpenAI integration
2025-06-15 21:17:27,976 - __main__ - INFO - Starting Server Agent...
2025-06-15 21:17:27,976 - __main__ - INFO - Starting MCP server...
2025-06-15 21:17:27,978 - __main__ - INFO - MCP server started successfully
2025-06-15 21:17:29,984 - __main__ - INFO - Starting WebSocket server on localhost:8765
2025-06-15 21:17:30,008 - websockets.server - INFO - server listening on 127.0.0.1:8765
2025-06-15 21:17:30,008 - websockets.server - INFO - server listening on [::1]:8765
2025-06-15 21:17:30,008 - __main__ - INFO - WebSocket server started successfully
2025-06-15 21:17:30,008 - __main__ - INFO - Server Agent ready to process requests
✅ Starting Client Agent (HTTP server on port 8766)...

🎉 AI Agents started successfully!
📡 Server Agent (WebSocket): ws://localhost:8765
🌐 Client Agent (HTTP): http://localhost:8766
Server Agent PID: 38750
Client Agent PID: 38757

Press Ctrl+C to stop all agents
2025-06-15 21:17:32,876 - asyncio - DEBUG - Using selector: KqueueSelector
2025-06-15 21:17:32,896 - __main__ - INFO - Client Agent initialized with OpenAI integration
2025-06-15 21:17:32,896 - __main__ - INFO - Starting Client Agent...
2025-06-15 21:17:32,960 - __main__ - INFO - Starting Client Agent HTTP server on port 8766
INFO:     Started server process [38757]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8766 (Press CTRL+C to quit)
2025-06-15 21:17:34,149 - __main__ - INFO - Starting conversation for user: DEFAULT_USER
INFO:     127.0.0.1:55774 - "POST /start_conversation HTTP/1.1" 200 OK
2025-06-15 21:17:48,034 - __main__ - INFO - Processing input from user DEFAULT_USER: show my accounts
2025-06-15 21:17:48,034 - __main__ - INFO - Understanding user intent: show my accounts
2025-06-15 21:17:48,137 - openai._base_client - DEBUG - Request options: {'method': 'post', 'url': '/chat/completions', 'files': None, 'idempotency_key': 'stainless-python-retry-92dd22d5-2ca5-41da-a01c-f211c0e82729', 'json_data': {'messages': [{'role': 'system', 'content': '\nYou are a friendly banking assistant helping users with money transfers and account management.\n\nYour role is to:\n1. Understand what the user wants to do\n2. Guide them through the process conversationally\n3. Ask for missing information in a natural way\n4. Provide helpful suggestions and options\n5. Explain banking concepts when needed\n6. No login or authentication is required - users can perform all operations directly\n\nAvailable services:\n- Create new accounts (checking/savings)\n- Check account balances\n- Transfer money between accounts (immediate or scheduled)\n- Set up recurring payments (weekly/bi-weekly/monthly)\n- View transaction history\n- Manage existing recurring payments\n\nRespond in JSON format:\n{\n    "intent": "detected_intent",\n    "confidence": 0.0-1.0,\n    "response_type": "question|confirmation|information|error",\n    "message": "conversational_response_to_user",\n    "needs_clarification": boolean,\n    "clarification_questions": [list_of_questions],\n    "ready_for_execution": boolean,\n    "extracted_params": {parameters_found},\n    "suggestions": [helpful_suggestions]\n}\n'}, {'role': 'user', 'content': "Conversation history:\nassistant: Hello! 👋 I'm your personal banking assistant.\n\nI can help you with:\n• Checking account balances\n• Transferring money between accounts\n• Setting up recurring payments\n• Viewing transaction history\n• Creating new accounts\n\nNo login required - just tell me what you'd like to do!\n\nWhat would you like to do today?\nuser: show my accounts\n\nCurrent message: show my accounts\n\nNote: No login required - user can perform all banking operations directly."}], 'model': 'gpt-4', 'temperature': 0.3}}
2025-06-15 21:17:48,148 - openai._base_client - DEBUG - Sending HTTP Request: POST https://api.openai.com/v1/chat/completions
2025-06-15 21:17:48,149 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=5.0 socket_options=None
2025-06-15 21:17:48,222 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x106346e50>
2025-06-15 21:17:48,222 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x103860b90> server_hostname='api.openai.com' timeout=5.0
2025-06-15 21:17:48,241 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x1063470d0>
2025-06-15 21:17:48,241 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-06-15 21:17:48,241 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-06-15 21:17:48,241 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-06-15 21:17:48,241 - httpcore.http11 - DEBUG - send_request_body.complete
2025-06-15 21:17:48,241 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-06-15 21:17:52,728 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Mon, 16 Jun 2025 02:17:52 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'user-scbswha3mb0dvdwske2hsfic'), (b'openai-processing-ms', b'3984'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'3992'), (b'x-ratelimit-limit-requests', b'10000'), (b'x-ratelimit-limit-tokens', b'10000'), (b'x-ratelimit-remaining-requests', b'9999'), (b'x-ratelimit-remaining-tokens', b'9599'), (b'x-ratelimit-reset-requests', b'8.64s'), (b'x-ratelimit-reset-tokens', b'2.406s'), (b'x-request-id', b'req_10a5ee2fdb05f4c20f0968e1b4d8f2d6'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=vah.CH9w6gO57FxYbHRoPjg0foO_VMVF2zV0TbRwgLU-1750040272-1.0.1.1-Yd1BswHvfCmNWbWF3Duy39Th_8PJi9vwWZofFoAjEXpStGOPnDuzGJ21Egqkj7zH6lSM9GJkeVDxV7hzAn9mg42cHEEhSysv19i2fSEu.Bc; path=/; expires=Mon, 16-Jun-25 02:47:52 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=48I3Fo8cZrvqn_5KNCNg.FAtou6Y17Nppe0zUKktTvw-*************-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'9506d01cab1d710b-DFW'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-06-15 21:17:52,730 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-15 21:17:52,731 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-06-15 21:17:52,731 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-06-15 21:17:52,731 - httpcore.http11 - DEBUG - response_closed.started
2025-06-15 21:17:52,731 - httpcore.http11 - DEBUG - response_closed.complete
2025-06-15 21:17:52,732 - openai._base_client - DEBUG - HTTP Response: POST https://api.openai.com/v1/chat/completions "200 OK" Headers([('date', 'Mon, 16 Jun 2025 02:17:52 GMT'), ('content-type', 'application/json'), ('transfer-encoding', 'chunked'), ('connection', 'keep-alive'), ('access-control-expose-headers', 'X-Request-ID'), ('openai-organization', 'user-scbswha3mb0dvdwske2hsfic'), ('openai-processing-ms', '3984'), ('openai-version', '2020-10-01'), ('x-envoy-upstream-service-time', '3992'), ('x-ratelimit-limit-requests', '10000'), ('x-ratelimit-limit-tokens', '10000'), ('x-ratelimit-remaining-requests', '9999'), ('x-ratelimit-remaining-tokens', '9599'), ('x-ratelimit-reset-requests', '8.64s'), ('x-ratelimit-reset-tokens', '2.406s'), ('x-request-id', 'req_10a5ee2fdb05f4c20f0968e1b4d8f2d6'), ('strict-transport-security', 'max-age=31536000; includeSubDomains; preload'), ('cf-cache-status', 'DYNAMIC'), ('set-cookie', '__cf_bm=vah.CH9w6gO57FxYbHRoPjg0foO_VMVF2zV0TbRwgLU-1750040272-1.0.1.1-Yd1BswHvfCmNWbWF3Duy39Th_8PJi9vwWZofFoAjEXpStGOPnDuzGJ21Egqkj7zH6lSM9GJkeVDxV7hzAn9mg42cHEEhSysv19i2fSEu.Bc; path=/; expires=Mon, 16-Jun-25 02:47:52 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), ('x-content-type-options', 'nosniff'), ('set-cookie', '_cfuvid=48I3Fo8cZrvqn_5KNCNg.FAtou6Y17Nppe0zUKktTvw-*************-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), ('server', 'cloudflare'), ('cf-ray', '9506d01cab1d710b-DFW'), ('content-encoding', 'gzip'), ('alt-svc', 'h3=":443"; ma=86400')])
2025-06-15 21:17:52,732 - openai._base_client - DEBUG - request_id: req_10a5ee2fdb05f4c20f0968e1b4d8f2d6
2025-06-15 21:17:52,735 - __main__ - INFO - Intent analysis: {'intent': 'check_account_balance', 'confidence': 0.9, 'response_type': 'information', 'message': 'Sure, I can help with that. You have two accounts with us: a checking account and a savings account. Would you like to check the balance for both or just one of them?', 'needs_clarification': False, 'clarification_questions': [], 'ready_for_execution': False, 'extracted_params': {}, 'suggestions': ['Check balance for both accounts', 'Check balance for checking account', 'Check balance for savings account']}
2025-06-15 21:17:52,736 - __main__ - INFO - Generating conversational response
2025-06-15 21:17:52,736 - __main__ - INFO - Generated response: Sure, I can help with that. You have two accounts with us: a checking account and a savings account. Would you like to check the balance for both or just one of them?

Some things I can help with:
• Check balance for both accounts
• Check balance for checking account
• Check balance for savings account
INFO:     127.0.0.1:55811 - "POST /chat HTTP/1.1" 200 OK
2025-06-15 21:19:43,662 - __main__ - INFO - Processing input from user DEFAULT_USER: i dont see a response
2025-06-15 21:19:43,662 - __main__ - INFO - Understanding user intent: i dont see a response
2025-06-15 21:19:43,663 - openai._base_client - DEBUG - Request options: {'method': 'post', 'url': '/chat/completions', 'files': None, 'idempotency_key': 'stainless-python-retry-9d98e85c-6304-4334-8a32-40dbf267ba6e', 'json_data': {'messages': [{'role': 'system', 'content': '\nYou are a friendly banking assistant helping users with money transfers and account management.\n\nYour role is to:\n1. Understand what the user wants to do\n2. Guide them through the process conversationally\n3. Ask for missing information in a natural way\n4. Provide helpful suggestions and options\n5. Explain banking concepts when needed\n6. No login or authentication is required - users can perform all operations directly\n\nAvailable services:\n- Create new accounts (checking/savings)\n- Check account balances\n- Transfer money between accounts (immediate or scheduled)\n- Set up recurring payments (weekly/bi-weekly/monthly)\n- View transaction history\n- Manage existing recurring payments\n\nRespond in JSON format:\n{\n    "intent": "detected_intent",\n    "confidence": 0.0-1.0,\n    "response_type": "question|confirmation|information|error",\n    "message": "conversational_response_to_user",\n    "needs_clarification": boolean,\n    "clarification_questions": [list_of_questions],\n    "ready_for_execution": boolean,\n    "extracted_params": {parameters_found},\n    "suggestions": [helpful_suggestions]\n}\n'}, {'role': 'user', 'content': "Conversation history:\nassistant: Hello! 👋 I'm your personal banking assistant.\n\nI can help you with:\n• Checking account balances\n• Transferring money between accounts\n• Setting up recurring payments\n• Viewing transaction history\n• Creating new accounts\n\nNo login required - just tell me what you'd like to do!\n\nWhat would you like to do today?\nuser: show my accounts\nassistant: Sure, I can help with that. You have two accounts with us: a checking account and a savings account. Would you like to check the balance for both or just one of them?\n\nSome things I can help with:\n• Check balance for both accounts\n• Check balance for checking account\n• Check balance for savings account\nuser: i dont see a response\n\nCurrent message: i dont see a response\n\nNote: No login required - user can perform all banking operations directly."}], 'model': 'gpt-4', 'temperature': 0.3}}
2025-06-15 21:19:43,663 - openai._base_client - DEBUG - Sending HTTP Request: POST https://api.openai.com/v1/chat/completions
2025-06-15 21:19:43,664 - httpcore.connection - DEBUG - close.started
2025-06-15 21:19:43,665 - httpcore.connection - DEBUG - close.complete
2025-06-15 21:19:43,665 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=5.0 socket_options=None
2025-06-15 21:19:43,755 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x10636b990>
2025-06-15 21:19:43,755 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x103860b90> server_hostname='api.openai.com' timeout=5.0
2025-06-15 21:19:43,782 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x10636b9d0>
2025-06-15 21:19:43,783 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-06-15 21:19:43,783 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-06-15 21:19:43,783 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-06-15 21:19:43,783 - httpcore.http11 - DEBUG - send_request_body.complete
2025-06-15 21:19:43,783 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-06-15 21:19:51,211 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Mon, 16 Jun 2025 02:19:51 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'user-scbswha3mb0dvdwske2hsfic'), (b'openai-processing-ms', b'6891'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'6901'), (b'x-ratelimit-limit-requests', b'10000'), (b'x-ratelimit-limit-tokens', b'10000'), (b'x-ratelimit-remaining-requests', b'9999'), (b'x-ratelimit-remaining-tokens', b'9510'), (b'x-ratelimit-reset-requests', b'8.64s'), (b'x-ratelimit-reset-tokens', b'2.94s'), (b'x-request-id', b'req_4edd7c55cb7e3dc6f7b9628a2c959526'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'X-Content-Type-Options', b'nosniff'), (b'Server', b'cloudflare'), (b'CF-RAY', b'9506d2eec92af0a8-DFW'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-06-15 21:19:51,212 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-15 21:19:51,212 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-06-15 21:19:51,212 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-06-15 21:19:51,212 - httpcore.http11 - DEBUG - response_closed.started
2025-06-15 21:19:51,212 - httpcore.http11 - DEBUG - response_closed.complete
2025-06-15 21:19:51,213 - openai._base_client - DEBUG - HTTP Response: POST https://api.openai.com/v1/chat/completions "200 OK" Headers({'date': 'Mon, 16 Jun 2025 02:19:51 GMT', 'content-type': 'application/json', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'access-control-expose-headers': 'X-Request-ID', 'openai-organization': 'user-scbswha3mb0dvdwske2hsfic', 'openai-processing-ms': '6891', 'openai-version': '2020-10-01', 'x-envoy-upstream-service-time': '6901', 'x-ratelimit-limit-requests': '10000', 'x-ratelimit-limit-tokens': '10000', 'x-ratelimit-remaining-requests': '9999', 'x-ratelimit-remaining-tokens': '9510', 'x-ratelimit-reset-requests': '8.64s', 'x-ratelimit-reset-tokens': '2.94s', 'x-request-id': 'req_4edd7c55cb7e3dc6f7b9628a2c959526', 'strict-transport-security': 'max-age=31536000; includeSubDomains; preload', 'cf-cache-status': 'DYNAMIC', 'x-content-type-options': 'nosniff', 'server': 'cloudflare', 'cf-ray': '9506d2eec92af0a8-DFW', 'content-encoding': 'gzip', 'alt-svc': 'h3=":443"; ma=86400'})
2025-06-15 21:19:51,213 - openai._base_client - DEBUG - request_id: req_4edd7c55cb7e3dc6f7b9628a2c959526
2025-06-15 21:19:51,213 - __main__ - INFO - Intent analysis: {'intent': 'check_balance', 'confidence': 1.0, 'response_type': 'information', 'message': 'Apologies for the confusion. You have two accounts with us: a checking account and a savings account. Here are the balances:\n\n- Checking account: $3,500\n- Savings account: $7,500', 'needs_clarification': False, 'clarification_questions': [], 'ready_for_execution': True, 'extracted_params': {}, 'suggestions': ['Would you like to transfer money between accounts?', 'Would you like to set up a recurring payment?', 'Would you like to view your transaction history?']}
2025-06-15 21:19:51,213 - __main__ - INFO - Communicating with server agent: {'user_id': 'DEFAULT_USER', 'intent': 'check_balance', 'parameters': {}, 'request_text': 'i dont see a response'}
2025-06-15 21:19:51,224 - websockets.client - DEBUG - = connection is CONNECTING
2025-06-15 21:19:51,224 - websockets.client - DEBUG - > GET / HTTP/1.1
2025-06-15 21:19:51,224 - websockets.client - DEBUG - > Host: localhost:8765
2025-06-15 21:19:51,224 - websockets.client - DEBUG - > Upgrade: websocket
2025-06-15 21:19:51,224 - websockets.client - DEBUG - > Connection: Upgrade
2025-06-15 21:19:51,224 - websockets.client - DEBUG - > Sec-WebSocket-Key: 9YW/upVoeqVjSjHMFw/sOQ==
2025-06-15 21:19:51,224 - websockets.client - DEBUG - > Sec-WebSocket-Version: 13
2025-06-15 21:19:51,224 - websockets.client - DEBUG - > Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits
2025-06-15 21:19:51,224 - websockets.client - DEBUG - > User-Agent: Python/3.11 websockets/15.0.1
2025-06-15 21:19:51,224 - websockets.server - DEBUG - = connection is CONNECTING
2025-06-15 21:19:51,225 - websockets.server - DEBUG - < GET / HTTP/1.1
2025-06-15 21:19:51,226 - websockets.server - DEBUG - < Host: localhost:8765
2025-06-15 21:19:51,226 - websockets.server - DEBUG - < Upgrade: websocket
2025-06-15 21:19:51,226 - websockets.server - DEBUG - < Connection: Upgrade
2025-06-15 21:19:51,226 - websockets.server - DEBUG - < Sec-WebSocket-Key: 9YW/upVoeqVjSjHMFw/sOQ==
2025-06-15 21:19:51,226 - websockets.server - DEBUG - < Sec-WebSocket-Version: 13
2025-06-15 21:19:51,226 - websockets.server - DEBUG - < Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits
2025-06-15 21:19:51,226 - websockets.server - DEBUG - < User-Agent: Python/3.11 websockets/15.0.1
2025-06-15 21:19:51,226 - websockets.server - DEBUG - > HTTP/1.1 101 Switching Protocols
2025-06-15 21:19:51,227 - websockets.server - DEBUG - > Date: Mon, 16 Jun 2025 02:19:51 GMT
2025-06-15 21:19:51,227 - websockets.server - DEBUG - > Upgrade: websocket
2025-06-15 21:19:51,227 - websockets.server - DEBUG - > Connection: Upgrade
2025-06-15 21:19:51,227 - websockets.server - DEBUG - > Sec-WebSocket-Accept: yO8Z8dt5jfhocuihZbzGzVIOyTk=
2025-06-15 21:19:51,227 - websockets.server - DEBUG - > Sec-WebSocket-Extensions: permessage-deflate; server_max_window_bits=12; client_max_window_bits=12
2025-06-15 21:19:51,227 - websockets.server - DEBUG - > Server: Python/3.11 websockets/15.0.1
2025-06-15 21:19:51,227 - websockets.server - DEBUG - = connection is OPEN
2025-06-15 21:19:51,227 - websockets.server - INFO - connection open
2025-06-15 21:19:51,227 - websockets.client - DEBUG - < HTTP/1.1 101 Switching Protocols
2025-06-15 21:19:51,227 - websockets.client - DEBUG - < Date: Mon, 16 Jun 2025 02:19:51 GMT
2025-06-15 21:19:51,227 - websockets.client - DEBUG - < Upgrade: websocket
2025-06-15 21:19:51,227 - websockets.client - DEBUG - < Connection: Upgrade
2025-06-15 21:19:51,227 - websockets.client - DEBUG - < Sec-WebSocket-Accept: yO8Z8dt5jfhocuihZbzGzVIOyTk=
2025-06-15 21:19:51,227 - websockets.client - DEBUG - < Sec-WebSocket-Extensions: permessage-deflate; server_max_window_bits=12; client_max_window_bits=12
2025-06-15 21:19:51,227 - websockets.client - DEBUG - < Server: Python/3.11 websockets/15.0.1
2025-06-15 21:19:51,227 - websockets.client - DEBUG - = connection is OPEN
2025-06-15 21:19:51,227 - websockets.client - DEBUG - > TEXT '{"user_id": "DEFAULT_USER", "intent": "check_ba...i dont see a response"}' [113 bytes]
2025-06-15 21:19:51,227 - __main__ - INFO - Sent request to Server Agent: {'user_id': 'DEFAULT_USER', 'intent': 'check_balance', 'parameters': {}, 'request_text': 'i dont see a response'}
2025-06-15 21:19:51,227 - websockets.server - ERROR - connection handler failed
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/TransferProject/agents/venv/lib/python3.11/site-packages/websockets/asyncio/server.py", line 376, in conn_handler
    await self.handler(connection)
          ^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: ServerAgent.handle_websocket_connection() missing 1 required positional argument: 'path'
2025-06-15 21:19:51,228 - websockets.server - DEBUG - > CLOSE 1011 (internal error) [2 bytes]
2025-06-15 21:19:51,228 - websockets.server - DEBUG - = connection is CLOSING
2025-06-15 21:19:51,228 - websockets.client - DEBUG - < CLOSE 1011 (internal error) [2 bytes]
2025-06-15 21:19:51,228 - websockets.server - DEBUG - < TEXT '{"user_id": "DEFAULT_USER", "intent": "check_ba...i dont see a response"}' [113 bytes]
2025-06-15 21:19:51,228 - websockets.client - DEBUG - > CLOSE 1011 (internal error) [2 bytes]
2025-06-15 21:19:51,229 - websockets.client - DEBUG - = connection is CLOSING
2025-06-15 21:19:51,229 - websockets.server - DEBUG - < CLOSE 1011 (internal error) [2 bytes]
2025-06-15 21:19:51,229 - websockets.server - DEBUG - > EOF
2025-06-15 21:19:51,229 - websockets.server - DEBUG - x half-closing TCP connection
2025-06-15 21:19:51,229 - websockets.client - DEBUG - < EOF
2025-06-15 21:19:51,229 - websockets.client - DEBUG - > EOF
2025-06-15 21:19:51,229 - websockets.client - DEBUG - = connection is CLOSED
2025-06-15 21:19:51,229 - websockets.client - DEBUG - x half-closing TCP connection
2025-06-15 21:19:51,229 - websockets.server - DEBUG - < EOF
2025-06-15 21:19:51,229 - websockets.server - DEBUG - = connection is CLOSED
2025-06-15 21:19:51,229 - __main__ - ERROR - Error processing user input: module 'websockets.exceptions' has no attribute 'ConnectionRefused'
INFO:     127.0.0.1:56146 - "POST /chat HTTP/1.1" 200 OK
🛑 Stopping AI Agents...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [38757]
