🔧 Starting MCP Server...
Installing dependencies...
Requirement already satisfied: mcp>=1.0.0 in ./venv/lib/python3.11/site-packages (from -r requirements.txt (line 1)) (1.9.4)
Requirement already satisfied: requests>=2.31.0 in ./venv/lib/python3.11/site-packages (from -r requirements.txt (line 2)) (2.32.4)
Requirement already satisfied: pydantic>=2.0.0 in ./venv/lib/python3.11/site-packages (from -r requirements.txt (line 3)) (2.11.7)
Requirement already satisfied: fastapi>=0.104.0 in ./venv/lib/python3.11/site-packages (from -r requirements.txt (line 4)) (0.115.12)
Requirement already satisfied: uvicorn>=0.24.0 in ./venv/lib/python3.11/site-packages (from -r requirements.txt (line 5)) (0.34.3)
Requirement already satisfied: python-dotenv>=1.0.0 in ./venv/lib/python3.11/site-packages (from -r requirements.txt (line 6)) (1.1.0)
Requirement already satisfied: httpx>=0.25.0 in ./venv/lib/python3.11/site-packages (from -r requirements.txt (line 7)) (0.28.1)
Requirement already satisfied: anyio>=4.5 in ./venv/lib/python3.11/site-packages (from mcp>=1.0.0->-r requirements.txt (line 1)) (4.9.0)
Requirement already satisfied: httpx-sse>=0.4 in ./venv/lib/python3.11/site-packages (from mcp>=1.0.0->-r requirements.txt (line 1)) (0.4.0)
Requirement already satisfied: pydantic-settings>=2.5.2 in ./venv/lib/python3.11/site-packages (from mcp>=1.0.0->-r requirements.txt (line 1)) (2.9.1)
Requirement already satisfied: python-multipart>=0.0.9 in ./venv/lib/python3.11/site-packages (from mcp>=1.0.0->-r requirements.txt (line 1)) (0.0.20)
Requirement already satisfied: sse-starlette>=1.6.1 in ./venv/lib/python3.11/site-packages (from mcp>=1.0.0->-r requirements.txt (line 1)) (2.3.6)
Requirement already satisfied: starlette>=0.27 in ./venv/lib/python3.11/site-packages (from mcp>=1.0.0->-r requirements.txt (line 1)) (0.46.2)
Requirement already satisfied: charset_normalizer<4,>=2 in ./venv/lib/python3.11/site-packages (from requests>=2.31.0->-r requirements.txt (line 2)) (3.4.2)
Requirement already satisfied: idna<4,>=2.5 in ./venv/lib/python3.11/site-packages (from requests>=2.31.0->-r requirements.txt (line 2)) (3.10)
Requirement already satisfied: urllib3<3,>=1.21.1 in ./venv/lib/python3.11/site-packages (from requests>=2.31.0->-r requirements.txt (line 2)) (2.4.0)
Requirement already satisfied: certifi>=2017.4.17 in ./venv/lib/python3.11/site-packages (from requests>=2.31.0->-r requirements.txt (line 2)) (2025.6.15)
Requirement already satisfied: annotated-types>=0.6.0 in ./venv/lib/python3.11/site-packages (from pydantic>=2.0.0->-r requirements.txt (line 3)) (0.7.0)
Requirement already satisfied: pydantic-core==2.33.2 in ./venv/lib/python3.11/site-packages (from pydantic>=2.0.0->-r requirements.txt (line 3)) (2.33.2)
Requirement already satisfied: typing-extensions>=4.12.2 in ./venv/lib/python3.11/site-packages (from pydantic>=2.0.0->-r requirements.txt (line 3)) (4.14.0)
Requirement already satisfied: typing-inspection>=0.4.0 in ./venv/lib/python3.11/site-packages (from pydantic>=2.0.0->-r requirements.txt (line 3)) (0.4.1)
Requirement already satisfied: click>=7.0 in ./venv/lib/python3.11/site-packages (from uvicorn>=0.24.0->-r requirements.txt (line 5)) (8.2.1)
Requirement already satisfied: h11>=0.8 in ./venv/lib/python3.11/site-packages (from uvicorn>=0.24.0->-r requirements.txt (line 5)) (0.16.0)
Requirement already satisfied: httpcore==1.* in ./venv/lib/python3.11/site-packages (from httpx>=0.25.0->-r requirements.txt (line 7)) (1.0.9)
Requirement already satisfied: sniffio>=1.1 in ./venv/lib/python3.11/site-packages (from anyio>=4.5->mcp>=1.0.0->-r requirements.txt (line 1)) (1.3.1)

[notice] A new release of pip is available: 24.0 -> 25.1.1
[notice] To update, run: pip install --upgrade pip
✅ Starting MCP Server...
2025-06-15 21:39:41,997 - asyncio - DEBUG - Using selector: KqueueSelector
2025-06-15 21:39:41,998 - mcp.server.lowlevel.server - DEBUG - Initializing server 'transfer-mcp-server'
2025-06-15 21:39:42,026 - mcp.server.lowlevel.server - DEBUG - Registering handler for ListToolsRequest
2025-06-15 21:39:42,026 - mcp.server.lowlevel.server - DEBUG - Registering handler for CallToolRequest
2025-06-15 21:39:42,026 - __main__ - INFO - Starting Transfer MCP Server...
  + Exception Group Traceback (most recent call last):
  |   File "/Users/<USER>/Downloads/TransferProject/mcp-server/server.py", line 524, in <module>
  |     asyncio.run(main())
  |   File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/runners.py", line 190, in run
  |     return runner.run(main)
  |            ^^^^^^^^^^^^^^^^
  |   File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/runners.py", line 118, in run
  |     return self._loop.run_until_complete(task)
  |            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  |   File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/base_events.py", line 654, in run_until_complete
  |     return future.result()
  |            ^^^^^^^^^^^^^^^
  |   File "/Users/<USER>/Downloads/TransferProject/mcp-server/server.py", line 521, in main
  |     await server.run()
  |   File "/Users/<USER>/Downloads/TransferProject/mcp-server/server.py", line 504, in run
  |     async with stdio_server() as (read_stream, write_stream):
  |   File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/contextlib.py", line 231, in __aexit__
  |     await self.gen.athrow(typ, value, traceback)
  |   File "/Users/<USER>/Downloads/TransferProject/mcp-server/venv/lib/python3.11/site-packages/mcp/server/stdio.py", line 85, in stdio_server
  |     async with anyio.create_task_group() as tg:
  |   File "/Users/<USER>/Downloads/TransferProject/mcp-server/venv/lib/python3.11/site-packages/anyio/_backends/_asyncio.py", line 772, in __aexit__
  |     raise BaseExceptionGroup(
  | ExceptionGroup: unhandled errors in a TaskGroup (1 sub-exception)
  +-+---------------- 1 ----------------
    | Traceback (most recent call last):
    |   File "/Users/<USER>/Downloads/TransferProject/mcp-server/venv/lib/python3.11/site-packages/mcp/server/stdio.py", line 88, in stdio_server
    |     yield read_stream, write_stream
    |   File "/Users/<USER>/Downloads/TransferProject/mcp-server/server.py", line 511, in run
    |     capabilities=self.server.get_capabilities(
    |                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/Users/<USER>/Downloads/TransferProject/mcp-server/venv/lib/python3.11/site-packages/mcp/server/lowlevel/server.py", line 198, in get_capabilities
    |     tools_capability = types.ToolsCapability(listChanged=notification_options.tools_changed)
    |                                                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    | AttributeError: 'NoneType' object has no attribute 'tools_changed'
    +------------------------------------
