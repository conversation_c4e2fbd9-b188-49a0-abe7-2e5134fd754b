🚀 Starting Spring Boot Backend...
Building application...
WARNING: A restricted method in java.lang.System has been called
WARNING: java.lang.System::load has been called by net.rubygrapefruit.platform.internal.NativeLibraryLoader in an unnamed module (file:/Users/<USER>/.gradle/wrapper/dists/gradle-8.13-bin/5xuhj0ry160q40clulazy9h7d/gradle-8.13/lib/native-platform-0.22-milestone-28.jar)
WARNING: Use --enable-native-access=ALL-UNNAMED to avoid a warning for callers in this module
WARNING: Restricted methods will be blocked in a future release unless native access is enabled


FAILURE: <PERSON>uild failed with an exception.

* What went wrong:
Could not determine the dependencies of task ':check'.
> Could not create task ':test'.
   > Could not create task of type 'Test'.
      > Could not create an instance of type org.gradle.api.internal.tasks.testing.DefaultTestTaskReports.
         > Could not create an instance of type org.gradle.api.reporting.internal.DefaultReportContainer.
            > Type T not present

* Try:
> Run with --stacktrace option to get the stack trace.
> Run with --info or --debug option to get more log output.
> Run with --scan to get full insights.
> Get more help at https://help.gradle.org.

BUILD FAILED in 292ms
❌ Build failed. Please check the errors above.
