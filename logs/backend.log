🚀 Starting Spring Boot Backend...
Building application...
WARNING: A restricted method in java.lang.System has been called
WARNING: java.lang.System::load has been called by net.rubygrapefruit.platform.internal.NativeLibraryLoader in an unnamed module (file:/Users/<USER>/.gradle/wrapper/dists/gradle-8.13-bin/5xuhj0ry160q40clulazy9h7d/gradle-8.13/lib/native-platform-0.22-milestone-28.jar)
WARNING: Use --enable-native-access=ALL-UNNAMED to avoid a warning for callers in this module
WARNING: Restricted methods will be blocked in a future release unless native access is enabled

> Task :clean
> Task :compileJava
> Task :processResources
> Task :classes
> Task :resolveMainClassName
> Task :bootJar

[Incubating] Problems report is available at: file:///Users/<USER>/Downloads/TransferProject/build/reports/problems/problems-report.html

Deprecated Gradle features were used in this build, making it incompatible with Gradle 9.0.

You can use '--warning-mode all' to show the individual deprecation warnings and determine if they come from your own scripts or plugins.

For more on this, please refer to https://docs.gradle.org/8.13/userguide/command_line_interface.html#sec:command_line_warnings in the Gradle documentation.

BUILD SUCCESSFUL in 811ms
5 actionable tasks: 5 executed
✅ Build successful. Starting application...
WARNING: A restricted method in java.lang.System has been called
WARNING: java.lang.System::load has been called by net.rubygrapefruit.platform.internal.NativeLibraryLoader in an unnamed module (file:/Users/<USER>/.gradle/wrapper/dists/gradle-8.13-bin/5xuhj0ry160q40clulazy9h7d/gradle-8.13/lib/native-platform-0.22-milestone-28.jar)
WARNING: Use --enable-native-access=ALL-UNNAMED to avoid a warning for callers in this module
WARNING: Restricted methods will be blocked in a future release unless native access is enabled

> Task :compileJava UP-TO-DATE
> Task :processResources UP-TO-DATE
> Task :classes UP-TO-DATE
> Task :resolveMainClassName UP-TO-DATE

> Task :bootRun FAILED

  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/
[32m :: Spring Boot :: [39m              [2m (v3.1.5)[0;39m

2025-06-15 21:27:35 - Starting TransferApplication using Java 24.0.1 with PID 38896 (/Users/<USER>/Downloads/TransferProject/build/classes/java/main started by akhildoddi in /Users/<USER>/Downloads/TransferProject)
2025-06-15 21:27:35 - Running with Spring Boot v3.1.5, Spring v6.0.13
2025-06-15 21:27:35 - No active profile set, falling back to 1 default profile: "default"
2025-06-15 21:27:35 - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-15 21:27:35 - Finished Spring Data repository scanning in 12 ms. Found 3 JPA repository interfaces.
WARNING: A restricted method in java.lang.System has been called
WARNING: java.lang.System::load has been called by org.apache.tomcat.jni.Library in an unnamed module (file:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.apache.tomcat.embed/tomcat-embed-core/10.1.15/e3a725405f494abc0fd4f30263c2e6ad87052de1/tomcat-embed-core-10.1.15.jar)
WARNING: Use --enable-native-access=ALL-UNNAMED to avoid a warning for callers in this module
WARNING: Restricted methods will be blocked in a future release unless native access is enabled

2025-06-15 21:27:36 - Tomcat initialized with port(s): 8080 (http)
2025-06-15 21:27:36 - Starting service [Tomcat]
2025-06-15 21:27:36 - Starting Servlet engine: [Apache Tomcat/10.1.15]
2025-06-15 21:27:36 - Initializing Spring embedded WebApplicationContext
2025-06-15 21:27:36 - Root WebApplicationContext: initialization completed in 369 ms
2025-06-15 21:27:36 - HikariPool-1 - Starting...
2025-06-15 21:27:36 - HikariPool-1 - Added connection conn0: url=jdbc:h2:mem:transferdb user=SA
2025-06-15 21:27:36 - HikariPool-1 - Start completed.
2025-06-15 21:27:36 - H2 console available at '/h2-console'. Database available at 'jdbc:h2:mem:transferdb'
2025-06-15 21:27:36 - Filter 'webMvcObservationFilter' configured for use
2025-06-15 21:27:36 - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-15 21:27:36 - HHH000412: Hibernate ORM core version 6.2.13.Final
2025-06-15 21:27:36 - HHH000406: Using bytecode reflection optimizer
2025-06-15 21:27:36 - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-15 21:27:36 - HHH90000025: H2Dialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-15 21:27:36 - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-15 21:27:36 - 
    drop table if exists accounts cascade 
Hibernate: 
    drop table if exists accounts cascade 
2025-06-15 21:27:36 - 
    drop table if exists recurring_payments cascade 
Hibernate: 
    drop table if exists recurring_payments cascade 
2025-06-15 21:27:36 - 
    drop table if exists transactions cascade 
Hibernate: 
    drop table if exists transactions cascade 
2025-06-15 21:27:36 - 
    create table accounts (
        balance numeric(19,2) not null,
        created_at timestamp(6) not null,
        id bigint generated by default as identity,
        updated_at timestamp(6),
        account_id varchar(255) not null unique,
        account_name varchar(255) not null,
        account_type varchar(255) not null check (account_type in ('CHECKING','SAVINGS')),
        customer_id varchar(255) not null,
        primary key (id)
    )
Hibernate: 
    create table accounts (
        balance numeric(19,2) not null,
        created_at timestamp(6) not null,
        id bigint generated by default as identity,
        updated_at timestamp(6),
        account_id varchar(255) not null unique,
        account_name varchar(255) not null,
        account_type varchar(255) not null check (account_type in ('CHECKING','SAVINGS')),
        customer_id varchar(255) not null,
        primary key (id)
    )
2025-06-15 21:27:36 - 
    create table recurring_payments (
        amount numeric(19,2) not null,
        end_date date,
        next_payment_date date not null,
        start_date date not null,
        created_at timestamp(6) not null,
        id bigint generated by default as identity,
        last_executed_at timestamp(6),
        updated_at timestamp(6),
        customer_id varchar(255) not null,
        description varchar(255),
        frequency varchar(255) not null check (frequency in ('WEEKLY','BI_WEEKLY','MONTHLY')),
        from_account_id varchar(255) not null,
        recurring_payment_id varchar(255) not null unique,
        status varchar(255) not null check (status in ('ACTIVE','PAUSED','CANCELLED','COMPLETED')),
        to_account_id varchar(255) not null,
        primary key (id)
    )
Hibernate: 
    create table recurring_payments (
        amount numeric(19,2) not null,
        end_date date,
        next_payment_date date not null,
        start_date date not null,
        created_at timestamp(6) not null,
        id bigint generated by default as identity,
        last_executed_at timestamp(6),
        updated_at timestamp(6),
        customer_id varchar(255) not null,
        description varchar(255),
        frequency varchar(255) not null check (frequency in ('WEEKLY','BI_WEEKLY','MONTHLY')),
        from_account_id varchar(255) not null,
        recurring_payment_id varchar(255) not null unique,
        status varchar(255) not null check (status in ('ACTIVE','PAUSED','CANCELLED','COMPLETED')),
        to_account_id varchar(255) not null,
        primary key (id)
    )
2025-06-15 21:27:36 - 
    create table transactions (
        amount numeric(19,2) not null,
        created_at timestamp(6) not null,
        id bigint generated by default as identity,
        processed_at timestamp(6),
        customer_id varchar(255) not null,
        description varchar(255),
        from_account_id varchar(255) not null,
        recurring_payment_id varchar(255),
        status varchar(255) not null check (status in ('PENDING','COMPLETED','FAILED','CANCELLED')),
        to_account_id varchar(255) not null,
        transaction_id varchar(255) not null unique,
        transaction_type varchar(255) not null check (transaction_type in ('IMMEDIATE','RECURRING')),
        primary key (id)
    )
Hibernate: 
    create table transactions (
        amount numeric(19,2) not null,
        created_at timestamp(6) not null,
        id bigint generated by default as identity,
        processed_at timestamp(6),
        customer_id varchar(255) not null,
        description varchar(255),
        from_account_id varchar(255) not null,
        recurring_payment_id varchar(255),
        status varchar(255) not null check (status in ('PENDING','COMPLETED','FAILED','CANCELLED')),
        to_account_id varchar(255) not null,
        transaction_id varchar(255) not null unique,
        transaction_type varchar(255) not null check (transaction_type in ('IMMEDIATE','RECURRING')),
        primary key (id)
    )
2025-06-15 21:27:36 - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-15 21:27:36 - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-15 21:27:36 - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-15 21:27:36 - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-06-15 21:27:36 - 31 mappings in 'requestMappingHandlerMapping'
2025-06-15 21:27:36 - Patterns [/webjars/**, /**] in 'resourceHandlerMapping'
2025-06-15 21:27:36 - ControllerAdvice beans: 0 @ExceptionHandler, 1 ResponseBodyAdvice
2025-06-15 21:27:36 - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-06-15 21:27:36 - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
2025-06-15 21:27:36 - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-15 21:27:36 - 
    drop table if exists accounts cascade 
Hibernate: 
    drop table if exists accounts cascade 
2025-06-15 21:27:36 - 
    drop table if exists recurring_payments cascade 
Hibernate: 
    drop table if exists recurring_payments cascade 
2025-06-15 21:27:36 - 
    drop table if exists transactions cascade 
Hibernate: 
    drop table if exists transactions cascade 
2025-06-15 21:27:36 - HikariPool-1 - Shutdown initiated...
2025-06-15 21:27:36 - HikariPool-1 - Shutdown completed.
2025-06-15 21:27:36 - Stopping service [Tomcat]
2025-06-15 21:27:36 - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-06-15 21:27:36 - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8080 was already in use.

Action:

Identify and stop the process that's listening on port 8080 or configure this application to listen on another port.


[Incubating] Problems report is available at: file:///Users/<USER>/Downloads/TransferProject/build/reports/problems/problems-report.html

FAILURE: Build failed with an exception.

* What went wrong:
Execution failed for task ':bootRun'.
> Process 'command '/Users/<USER>/Library/Java/JavaVirtualMachines/openjdk-24.0.1/Contents/Home/bin/java'' finished with non-zero exit value 1

* Try:
> Run with --stacktrace option to get the stack trace.
> Run with --info or --debug option to get more log output.
> Run with --scan to get full insights.
> Get more help at https://help.gradle.org.

Deprecated Gradle features were used in this build, making it incompatible with Gradle 9.0.

You can use '--warning-mode all' to show the individual deprecation warnings and determine if they come from your own scripts or plugins.

For more on this, please refer to https://docs.gradle.org/8.13/userguide/command_line_interface.html#sec:command_line_warnings in the Gradle documentation.

BUILD FAILED in 1s
4 actionable tasks: 1 executed, 3 up-to-date
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  2025-06-15 21:27:41 - Processing due recurring payments
2025-06-15 21:27:41 - 
    /* SELECT
        rp 
    FROM
        RecurringPayment rp 
    WHERE
        rp.status = :status 
        AND rp.nextPaymentDate <= :date */ select
            r1_0.id,
            r1_0.amount,
            r1_0.created_at,
            r1_0.customer_id,
            r1_0.description,
            r1_0.end_date,
            r1_0.frequency,
            r1_0.from_account_id,
            r1_0.last_executed_at,
            r1_0.next_payment_date,
            r1_0.recurring_payment_id,
            r1_0.start_date,
            r1_0.status,
            r1_0.to_account_id,
            r1_0.updated_at 
        from
            recurring_payments r1_0 
        where
            r1_0.status=? 
            and r1_0.next_payment_date<=?
Hibernate: 
    /* SELECT
        rp 
    FROM
        RecurringPayment rp 
    WHERE
        rp.status = :status 
        AND rp.nextPaymentDate <= :date */ select
            r1_0.id,
            r1_0.amount,
            r1_0.created_at,
            r1_0.customer_id,
            r1_0.description,
            r1_0.end_date,
            r1_0.frequency,
            r1_0.from_account_id,
            r1_0.last_executed_at,
            r1_0.next_payment_date,
            r1_0.recurring_payment_id,
            r1_0.start_date,
            r1_0.status,
            r1_0.to_account_id,
            r1_0.updated_at 
        from
            recurring_payments r1_0 
        where
            r1_0.status=? 
            and r1_0.next_payment_date<=?
2025-06-15 21:27:41 - Found 0 due recurring payments
2025-06-15 21:27:54 - GET "/actuator/health", parameters={}
2025-06-15 21:27:54 - Using 'application/vnd.spring-boot.actuator.v3+json', given [*/*] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-06-15 21:27:54 - Writing [org.springframework.boot.actuate.health.SystemHealth@2bc9969b]
2025-06-15 21:27:54 - Completed 200 OK
2025-06-15 21:28:41 - Processing due recurring payments
2025-06-15 21:28:41 - 
    /* SELECT
        rp 
    FROM
        RecurringPayment rp 
    WHERE
        rp.status = :status 
        AND rp.nextPaymentDate <= :date */ select
            r1_0.id,
            r1_0.amount,
            r1_0.created_at,
            r1_0.customer_id,
            r1_0.description,
            r1_0.end_date,
            r1_0.frequency,
            r1_0.from_account_id,
            r1_0.last_executed_at,
            r1_0.next_payment_date,
            r1_0.recurring_payment_id,
            r1_0.start_date,
            r1_0.status,
            r1_0.to_account_id,
            r1_0.updated_at 
        from
            recurring_payments r1_0 
        where
            r1_0.status=? 
            and r1_0.next_payment_date<=?
Hibernate: 
    /* SELECT
        rp 
    FROM
        RecurringPayment rp 
    WHERE
        rp.status = :status 
        AND rp.nextPaymentDate <= :date */ select
            r1_0.id,
            r1_0.amount,
            r1_0.created_at,
            r1_0.customer_id,
            r1_0.description,
            r1_0.end_date,
            r1_0.frequency,
            r1_0.from_account_id,
            r1_0.last_executed_at,
            r1_0.next_payment_date,
            r1_0.recurring_payment_id,
            r1_0.start_date,
            r1_0.status,
            r1_0.to_account_id,
            r1_0.updated_at 
        from
            recurring_payments r1_0 
        where
            r1_0.status=? 
            and r1_0.next_payment_date<=?
2025-06-15 21:28:41 - Found 0 due recurring payments
2025-06-15 21:29:41 - Processing due recurring payments
2025-06-15 21:29:41 - 
    /* SELECT
        rp 
    FROM
        RecurringPayment rp 
    WHERE
        rp.status = :status 
        AND rp.nextPaymentDate <= :date */ select
            r1_0.id,
            r1_0.amount,
            r1_0.created_at,
            r1_0.customer_id,
            r1_0.description,
            r1_0.end_date,
            r1_0.frequency,
            r1_0.from_account_id,
            r1_0.last_executed_at,
            r1_0.next_payment_date,
            r1_0.recurring_payment_id,
            r1_0.start_date,
            r1_0.status,
            r1_0.to_account_id,
            r1_0.updated_at 
        from
            recurring_payments r1_0 
        where
            r1_0.status=? 
            and r1_0.next_payment_date<=?
Hibernate: 
    /* SELECT
        rp 
    FROM
        RecurringPayment rp 
    WHERE
        rp.status = :status 
        AND rp.nextPaymentDate <= :date */ select
            r1_0.id,
            r1_0.amount,
            r1_0.created_at,
            r1_0.customer_id,
            r1_0.description,
            r1_0.end_date,
            r1_0.frequency,
            r1_0.from_account_id,
            r1_0.last_executed_at,
            r1_0.next_payment_date,
            r1_0.recurring_payment_id,
            r1_0.start_date,
            r1_0.status,
            r1_0.to_account_id,
            r1_0.updated_at 
        from
            recurring_payments r1_0 
        where
            r1_0.status=? 
            and r1_0.next_payment_date<=?
2025-06-15 21:29:41 - Found 0 due recurring payments
2025-06-15 21:30:41 - Processing due recurring payments
2025-06-15 21:30:41 - 
    /* SELECT
        rp 
    FROM
        RecurringPayment rp 
    WHERE
        rp.status = :status 
        AND rp.nextPaymentDate <= :date */ select
            r1_0.id,
            r1_0.amount,
            r1_0.created_at,
            r1_0.customer_id,
            r1_0.description,
            r1_0.end_date,
            r1_0.frequency,
            r1_0.from_account_id,
            r1_0.last_executed_at,
            r1_0.next_payment_date,
            r1_0.recurring_payment_id,
            r1_0.start_date,
            r1_0.status,
            r1_0.to_account_id,
            r1_0.updated_at 
        from
            recurring_payments r1_0 
        where
            r1_0.status=? 
            and r1_0.next_payment_date<=?
Hibernate: 
    /* SELECT
        rp 
    FROM
        RecurringPayment rp 
    WHERE
        rp.status = :status 
        AND rp.nextPaymentDate <= :date */ select
            r1_0.id,
            r1_0.amount,
            r1_0.created_at,
            r1_0.customer_id,
            r1_0.description,
            r1_0.end_date,
            r1_0.frequency,
            r1_0.from_account_id,
            r1_0.last_executed_at,
            r1_0.next_payment_date,
            r1_0.recurring_payment_id,
            r1_0.start_date,
            r1_0.status,
            r1_0.to_account_id,
            r1_0.updated_at 
        from
            recurring_payments r1_0 
        where
            r1_0.status=? 
            and r1_0.next_payment_date<=?
2025-06-15 21:30:41 - Found 0 due recurring payments
