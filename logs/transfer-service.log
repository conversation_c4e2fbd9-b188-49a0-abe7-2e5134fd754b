2025-06-15 21:04:39 [main] INFO  com.transfer.TransferApplication - Starting TransferApplication using Java 24.0.1 with PID 38489 (/Users/<USER>/Downloads/TransferProject/build/classes/java/main started by akhildoddi in /Users/<USER>/Downloads/TransferProject)
2025-06-15 21:04:39 [main] DEBUG com.transfer.TransferApplication - Running with Spring Boot v3.1.5, Spring v6.0.13
2025-06-15 21:04:39 [main] INFO  com.transfer.TransferApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-15 21:04:40 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-15 21:04:40 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 3 JPA repository interfaces.
2025-06-15 21:04:40 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - <PERSON><PERSON> initialized with port(s): 8080 (http)
2025-06-15 21:04:40 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-15 21:04:40 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.15]
2025-06-15 21:04:40 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-15 21:04:40 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 368 ms
2025-06-15 21:04:40 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-15 21:04:40 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection conn0: url=jdbc:h2:mem:transferdb user=SA
2025-06-15 21:04:40 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-15 21:04:40 [main] INFO  o.s.b.a.h.H2ConsoleAutoConfiguration - H2 console available at '/h2-console'. Database available at 'jdbc:h2:mem:transferdb'
2025-06-15 21:04:40 [main] DEBUG o.s.w.f.ServerHttpObservationFilter - Filter 'webMvcObservationFilter' configured for use
2025-06-15 21:04:40 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-15 21:04:40 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.2.13.Final
2025-06-15 21:04:40 [main] INFO  org.hibernate.cfg.Environment - HHH000406: Using bytecode reflection optimizer
2025-06-15 21:04:40 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-15 21:04:40 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: H2Dialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-15 21:04:40 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-15 21:04:40 [main] DEBUG org.hibernate.SQL - 
    drop table if exists accounts cascade 
2025-06-15 21:04:40 [main] DEBUG org.hibernate.SQL - 
    drop table if exists recurring_payments cascade 
2025-06-15 21:04:40 [main] DEBUG org.hibernate.SQL - 
    drop table if exists transactions cascade 
2025-06-15 21:04:40 [main] DEBUG org.hibernate.SQL - 
    create table accounts (
        balance numeric(19,2) not null,
        created_at timestamp(6) not null,
        id bigint generated by default as identity,
        updated_at timestamp(6),
        account_id varchar(255) not null unique,
        account_name varchar(255) not null,
        account_type varchar(255) not null check (account_type in ('CHECKING','SAVINGS')),
        customer_id varchar(255) not null,
        primary key (id)
    )
2025-06-15 21:04:40 [main] DEBUG org.hibernate.SQL - 
    create table recurring_payments (
        amount numeric(19,2) not null,
        end_date date,
        next_payment_date date not null,
        start_date date not null,
        created_at timestamp(6) not null,
        id bigint generated by default as identity,
        last_executed_at timestamp(6),
        updated_at timestamp(6),
        customer_id varchar(255) not null,
        description varchar(255),
        frequency varchar(255) not null check (frequency in ('WEEKLY','BI_WEEKLY','MONTHLY')),
        from_account_id varchar(255) not null,
        recurring_payment_id varchar(255) not null unique,
        status varchar(255) not null check (status in ('ACTIVE','PAUSED','CANCELLED','COMPLETED')),
        to_account_id varchar(255) not null,
        primary key (id)
    )
2025-06-15 21:04:40 [main] DEBUG org.hibernate.SQL - 
    create table transactions (
        amount numeric(19,2) not null,
        created_at timestamp(6) not null,
        id bigint generated by default as identity,
        processed_at timestamp(6),
        customer_id varchar(255) not null,
        description varchar(255),
        from_account_id varchar(255) not null,
        recurring_payment_id varchar(255),
        status varchar(255) not null check (status in ('PENDING','COMPLETED','FAILED','CANCELLED')),
        to_account_id varchar(255) not null,
        transaction_id varchar(255) not null unique,
        transaction_type varchar(255) not null check (transaction_type in ('IMMEDIATE','RECURRING')),
        primary key (id)
    )
2025-06-15 21:04:40 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-15 21:04:40 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-15 21:04:40 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-15 21:04:40 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-06-15 21:04:40 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 31 mappings in 'requestMappingHandlerMapping'
2025-06-15 21:04:40 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**] in 'resourceHandlerMapping'
2025-06-15 21:04:40 [main] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 0 @ExceptionHandler, 1 ResponseBodyAdvice
2025-06-15 21:04:40 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-06-15 21:04:41 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-15 21:04:41 [main] INFO  com.transfer.TransferApplication - Started TransferApplication in 1.327 seconds (process running for 1.424)
2025-06-15 21:04:41 [scheduling-1] DEBUG c.t.service.RecurringPaymentService - Processing due recurring payments
2025-06-15 21:04:41 [scheduling-1] DEBUG org.hibernate.SQL - 
    /* SELECT
        rp 
    FROM
        RecurringPayment rp 
    WHERE
        rp.status = :status 
        AND rp.nextPaymentDate <= :date */ select
            r1_0.id,
            r1_0.amount,
            r1_0.created_at,
            r1_0.customer_id,
            r1_0.description,
            r1_0.end_date,
            r1_0.frequency,
            r1_0.from_account_id,
            r1_0.last_executed_at,
            r1_0.next_payment_date,
            r1_0.recurring_payment_id,
            r1_0.start_date,
            r1_0.status,
            r1_0.to_account_id,
            r1_0.updated_at 
        from
            recurring_payments r1_0 
        where
            r1_0.status=? 
            and r1_0.next_payment_date<=?
2025-06-15 21:04:41 [scheduling-1] INFO  c.t.service.RecurringPaymentService - Found 0 due recurring payments
2025-06-15 21:04:58 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-15 21:04:58 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-15 21:04:58 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Detected StandardServletMultipartResolver
2025-06-15 21:04:58 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Detected AcceptHeaderLocaleResolver
2025-06-15 21:04:58 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Detected FixedThemeResolver
2025-06-15 21:04:58 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@648100de
2025-06-15 21:04:58 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.support.SessionFlashMapManager@5dcbdcb9
2025-06-15 21:04:58 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-06-15 21:04:58 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-15 21:04:58 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - GET "/actuator/health", parameters={}
2025-06-15 21:04:58 [http-nio-8080-exec-1] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Using 'application/vnd.spring-boot.actuator.v3+json', given [*/*] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-06-15 21:04:58 [http-nio-8080-exec-1] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Writing [org.springframework.boot.actuate.health.SystemHealth@57d45d11]
2025-06-15 21:04:58 [http-nio-8080-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-15 21:05:41 [scheduling-1] DEBUG c.t.service.RecurringPaymentService - Processing due recurring payments
2025-06-15 21:05:41 [scheduling-1] DEBUG org.hibernate.SQL - 
    /* SELECT
        rp 
    FROM
        RecurringPayment rp 
    WHERE
        rp.status = :status 
        AND rp.nextPaymentDate <= :date */ select
            r1_0.id,
            r1_0.amount,
            r1_0.created_at,
            r1_0.customer_id,
            r1_0.description,
            r1_0.end_date,
            r1_0.frequency,
            r1_0.from_account_id,
            r1_0.last_executed_at,
            r1_0.next_payment_date,
            r1_0.recurring_payment_id,
            r1_0.start_date,
            r1_0.status,
            r1_0.to_account_id,
            r1_0.updated_at 
        from
            recurring_payments r1_0 
        where
            r1_0.status=? 
            and r1_0.next_payment_date<=?
2025-06-15 21:05:41 [scheduling-1] INFO  c.t.service.RecurringPaymentService - Found 0 due recurring payments
2025-06-15 21:06:41 [scheduling-1] DEBUG c.t.service.RecurringPaymentService - Processing due recurring payments
2025-06-15 21:06:41 [scheduling-1] DEBUG org.hibernate.SQL - 
    /* SELECT
        rp 
    FROM
        RecurringPayment rp 
    WHERE
        rp.status = :status 
        AND rp.nextPaymentDate <= :date */ select
            r1_0.id,
            r1_0.amount,
            r1_0.created_at,
            r1_0.customer_id,
            r1_0.description,
            r1_0.end_date,
            r1_0.frequency,
            r1_0.from_account_id,
            r1_0.last_executed_at,
            r1_0.next_payment_date,
            r1_0.recurring_payment_id,
            r1_0.start_date,
            r1_0.status,
            r1_0.to_account_id,
            r1_0.updated_at 
        from
            recurring_payments r1_0 
        where
            r1_0.status=? 
            and r1_0.next_payment_date<=?
2025-06-15 21:06:41 [scheduling-1] INFO  c.t.service.RecurringPaymentService - Found 0 due recurring payments
2025-06-15 21:07:41 [scheduling-1] DEBUG c.t.service.RecurringPaymentService - Processing due recurring payments
2025-06-15 21:07:41 [scheduling-1] DEBUG org.hibernate.SQL - 
    /* SELECT
        rp 
    FROM
        RecurringPayment rp 
    WHERE
        rp.status = :status 
        AND rp.nextPaymentDate <= :date */ select
            r1_0.id,
            r1_0.amount,
            r1_0.created_at,
            r1_0.customer_id,
            r1_0.description,
            r1_0.end_date,
            r1_0.frequency,
            r1_0.from_account_id,
            r1_0.last_executed_at,
            r1_0.next_payment_date,
            r1_0.recurring_payment_id,
            r1_0.start_date,
            r1_0.status,
            r1_0.to_account_id,
            r1_0.updated_at 
        from
            recurring_payments r1_0 
        where
            r1_0.status=? 
            and r1_0.next_payment_date<=?
2025-06-15 21:07:41 [scheduling-1] INFO  c.t.service.RecurringPaymentService - Found 0 due recurring payments
2025-06-15 21:08:41 [scheduling-1] DEBUG c.t.service.RecurringPaymentService - Processing due recurring payments
2025-06-15 21:08:41 [scheduling-1] DEBUG org.hibernate.SQL - 
    /* SELECT
        rp 
    FROM
        RecurringPayment rp 
    WHERE
        rp.status = :status 
        AND rp.nextPaymentDate <= :date */ select
            r1_0.id,
            r1_0.amount,
            r1_0.created_at,
            r1_0.customer_id,
            r1_0.description,
            r1_0.end_date,
            r1_0.frequency,
            r1_0.from_account_id,
            r1_0.last_executed_at,
            r1_0.next_payment_date,
            r1_0.recurring_payment_id,
            r1_0.start_date,
            r1_0.status,
            r1_0.to_account_id,
            r1_0.updated_at 
        from
            recurring_payments r1_0 
        where
            r1_0.status=? 
            and r1_0.next_payment_date<=?
2025-06-15 21:08:41 [scheduling-1] INFO  c.t.service.RecurringPaymentService - Found 0 due recurring payments
2025-06-15 21:09:41 [scheduling-1] DEBUG c.t.service.RecurringPaymentService - Processing due recurring payments
2025-06-15 21:09:41 [scheduling-1] DEBUG org.hibernate.SQL - 
    /* SELECT
        rp 
    FROM
        RecurringPayment rp 
    WHERE
        rp.status = :status 
        AND rp.nextPaymentDate <= :date */ select
            r1_0.id,
            r1_0.amount,
            r1_0.created_at,
            r1_0.customer_id,
            r1_0.description,
            r1_0.end_date,
            r1_0.frequency,
            r1_0.from_account_id,
            r1_0.last_executed_at,
            r1_0.next_payment_date,
            r1_0.recurring_payment_id,
            r1_0.start_date,
            r1_0.status,
            r1_0.to_account_id,
            r1_0.updated_at 
        from
            recurring_payments r1_0 
        where
            r1_0.status=? 
            and r1_0.next_payment_date<=?
2025-06-15 21:09:41 [scheduling-1] INFO  c.t.service.RecurringPaymentService - Found 0 due recurring payments
2025-06-15 21:10:41 [scheduling-1] DEBUG c.t.service.RecurringPaymentService - Processing due recurring payments
2025-06-15 21:10:41 [scheduling-1] DEBUG org.hibernate.SQL - 
    /* SELECT
        rp 
    FROM
        RecurringPayment rp 
    WHERE
        rp.status = :status 
        AND rp.nextPaymentDate <= :date */ select
            r1_0.id,
            r1_0.amount,
            r1_0.created_at,
            r1_0.customer_id,
            r1_0.description,
            r1_0.end_date,
            r1_0.frequency,
            r1_0.from_account_id,
            r1_0.last_executed_at,
            r1_0.next_payment_date,
            r1_0.recurring_payment_id,
            r1_0.start_date,
            r1_0.status,
            r1_0.to_account_id,
            r1_0.updated_at 
        from
            recurring_payments r1_0 
        where
            r1_0.status=? 
            and r1_0.next_payment_date<=?
2025-06-15 21:10:41 [scheduling-1] INFO  c.t.service.RecurringPaymentService - Found 0 due recurring payments
2025-06-15 21:11:41 [scheduling-1] DEBUG c.t.service.RecurringPaymentService - Processing due recurring payments
2025-06-15 21:11:41 [scheduling-1] DEBUG org.hibernate.SQL - 
    /* SELECT
        rp 
    FROM
        RecurringPayment rp 
    WHERE
        rp.status = :status 
        AND rp.nextPaymentDate <= :date */ select
            r1_0.id,
            r1_0.amount,
            r1_0.created_at,
            r1_0.customer_id,
            r1_0.description,
            r1_0.end_date,
            r1_0.frequency,
            r1_0.from_account_id,
            r1_0.last_executed_at,
            r1_0.next_payment_date,
            r1_0.recurring_payment_id,
            r1_0.start_date,
            r1_0.status,
            r1_0.to_account_id,
            r1_0.updated_at 
        from
            recurring_payments r1_0 
        where
            r1_0.status=? 
            and r1_0.next_payment_date<=?
2025-06-15 21:11:41 [scheduling-1] INFO  c.t.service.RecurringPaymentService - Found 0 due recurring payments
2025-06-15 21:12:41 [scheduling-1] DEBUG c.t.service.RecurringPaymentService - Processing due recurring payments
2025-06-15 21:12:41 [scheduling-1] DEBUG org.hibernate.SQL - 
    /* SELECT
        rp 
    FROM
        RecurringPayment rp 
    WHERE
        rp.status = :status 
        AND rp.nextPaymentDate <= :date */ select
            r1_0.id,
            r1_0.amount,
            r1_0.created_at,
            r1_0.customer_id,
            r1_0.description,
            r1_0.end_date,
            r1_0.frequency,
            r1_0.from_account_id,
            r1_0.last_executed_at,
            r1_0.next_payment_date,
            r1_0.recurring_payment_id,
            r1_0.start_date,
            r1_0.status,
            r1_0.to_account_id,
            r1_0.updated_at 
        from
            recurring_payments r1_0 
        where
            r1_0.status=? 
            and r1_0.next_payment_date<=?
2025-06-15 21:12:41 [scheduling-1] INFO  c.t.service.RecurringPaymentService - Found 0 due recurring payments
2025-06-15 21:13:41 [scheduling-1] DEBUG c.t.service.RecurringPaymentService - Processing due recurring payments
2025-06-15 21:13:41 [scheduling-1] DEBUG org.hibernate.SQL - 
    /* SELECT
        rp 
    FROM
        RecurringPayment rp 
    WHERE
        rp.status = :status 
        AND rp.nextPaymentDate <= :date */ select
            r1_0.id,
            r1_0.amount,
            r1_0.created_at,
            r1_0.customer_id,
            r1_0.description,
            r1_0.end_date,
            r1_0.frequency,
            r1_0.from_account_id,
            r1_0.last_executed_at,
            r1_0.next_payment_date,
            r1_0.recurring_payment_id,
            r1_0.start_date,
            r1_0.status,
            r1_0.to_account_id,
            r1_0.updated_at 
        from
            recurring_payments r1_0 
        where
            r1_0.status=? 
            and r1_0.next_payment_date<=?
2025-06-15 21:13:41 [scheduling-1] INFO  c.t.service.RecurringPaymentService - Found 0 due recurring payments
2025-06-15 21:14:41 [scheduling-1] DEBUG c.t.service.RecurringPaymentService - Processing due recurring payments
2025-06-15 21:14:41 [scheduling-1] DEBUG org.hibernate.SQL - 
    /* SELECT
        rp 
    FROM
        RecurringPayment rp 
    WHERE
        rp.status = :status 
        AND rp.nextPaymentDate <= :date */ select
            r1_0.id,
            r1_0.amount,
            r1_0.created_at,
            r1_0.customer_id,
            r1_0.description,
            r1_0.end_date,
            r1_0.frequency,
            r1_0.from_account_id,
            r1_0.last_executed_at,
            r1_0.next_payment_date,
            r1_0.recurring_payment_id,
            r1_0.start_date,
            r1_0.status,
            r1_0.to_account_id,
            r1_0.updated_at 
        from
            recurring_payments r1_0 
        where
            r1_0.status=? 
            and r1_0.next_payment_date<=?
2025-06-15 21:14:41 [scheduling-1] INFO  c.t.service.RecurringPaymentService - Found 0 due recurring payments
2025-06-15 21:15:41 [scheduling-1] DEBUG c.t.service.RecurringPaymentService - Processing due recurring payments
2025-06-15 21:15:41 [scheduling-1] DEBUG org.hibernate.SQL - 
    /* SELECT
        rp 
    FROM
        RecurringPayment rp 
    WHERE
        rp.status = :status 
        AND rp.nextPaymentDate <= :date */ select
            r1_0.id,
            r1_0.amount,
            r1_0.created_at,
            r1_0.customer_id,
            r1_0.description,
            r1_0.end_date,
            r1_0.frequency,
            r1_0.from_account_id,
            r1_0.last_executed_at,
            r1_0.next_payment_date,
            r1_0.recurring_payment_id,
            r1_0.start_date,
            r1_0.status,
            r1_0.to_account_id,
            r1_0.updated_at 
        from
            recurring_payments r1_0 
        where
            r1_0.status=? 
            and r1_0.next_payment_date<=?
2025-06-15 21:15:41 [scheduling-1] INFO  c.t.service.RecurringPaymentService - Found 0 due recurring payments
2025-06-15 21:16:41 [scheduling-1] DEBUG c.t.service.RecurringPaymentService - Processing due recurring payments
2025-06-15 21:16:41 [scheduling-1] DEBUG org.hibernate.SQL - 
    /* SELECT
        rp 
    FROM
        RecurringPayment rp 
    WHERE
        rp.status = :status 
        AND rp.nextPaymentDate <= :date */ select
            r1_0.id,
            r1_0.amount,
            r1_0.created_at,
            r1_0.customer_id,
            r1_0.description,
            r1_0.end_date,
            r1_0.frequency,
            r1_0.from_account_id,
            r1_0.last_executed_at,
            r1_0.next_payment_date,
            r1_0.recurring_payment_id,
            r1_0.start_date,
            r1_0.status,
            r1_0.to_account_id,
            r1_0.updated_at 
        from
            recurring_payments r1_0 
        where
            r1_0.status=? 
            and r1_0.next_payment_date<=?
2025-06-15 21:16:41 [scheduling-1] INFO  c.t.service.RecurringPaymentService - Found 0 due recurring payments
2025-06-15 21:17:06 [main] INFO  com.transfer.TransferApplication - Starting TransferApplication using Java 24.0.1 with PID 38739 (/Users/<USER>/Downloads/TransferProject/build/classes/java/main started by akhildoddi in /Users/<USER>/Downloads/TransferProject)
2025-06-15 21:17:06 [main] DEBUG com.transfer.TransferApplication - Running with Spring Boot v3.1.5, Spring v6.0.13
2025-06-15 21:17:06 [main] INFO  com.transfer.TransferApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-15 21:17:06 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-15 21:17:06 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 3 JPA repository interfaces.
2025-06-15 21:17:07 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-15 21:17:07 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-15 21:17:07 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.15]
2025-06-15 21:17:07 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-15 21:17:07 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 373 ms
2025-06-15 21:17:07 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-15 21:17:07 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection conn0: url=jdbc:h2:mem:transferdb user=SA
2025-06-15 21:17:07 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-15 21:17:07 [main] INFO  o.s.b.a.h.H2ConsoleAutoConfiguration - H2 console available at '/h2-console'. Database available at 'jdbc:h2:mem:transferdb'
2025-06-15 21:17:07 [main] DEBUG o.s.w.f.ServerHttpObservationFilter - Filter 'webMvcObservationFilter' configured for use
2025-06-15 21:17:07 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-15 21:17:07 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.2.13.Final
2025-06-15 21:17:07 [main] INFO  org.hibernate.cfg.Environment - HHH000406: Using bytecode reflection optimizer
2025-06-15 21:17:07 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-15 21:17:07 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: H2Dialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-15 21:17:07 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-15 21:17:07 [main] DEBUG org.hibernate.SQL - 
    drop table if exists accounts cascade 
2025-06-15 21:17:07 [main] DEBUG org.hibernate.SQL - 
    drop table if exists recurring_payments cascade 
2025-06-15 21:17:07 [main] DEBUG org.hibernate.SQL - 
    drop table if exists transactions cascade 
2025-06-15 21:17:07 [main] DEBUG org.hibernate.SQL - 
    create table accounts (
        balance numeric(19,2) not null,
        created_at timestamp(6) not null,
        id bigint generated by default as identity,
        updated_at timestamp(6),
        account_id varchar(255) not null unique,
        account_name varchar(255) not null,
        account_type varchar(255) not null check (account_type in ('CHECKING','SAVINGS')),
        customer_id varchar(255) not null,
        primary key (id)
    )
2025-06-15 21:17:07 [main] DEBUG org.hibernate.SQL - 
    create table recurring_payments (
        amount numeric(19,2) not null,
        end_date date,
        next_payment_date date not null,
        start_date date not null,
        created_at timestamp(6) not null,
        id bigint generated by default as identity,
        last_executed_at timestamp(6),
        updated_at timestamp(6),
        customer_id varchar(255) not null,
        description varchar(255),
        frequency varchar(255) not null check (frequency in ('WEEKLY','BI_WEEKLY','MONTHLY')),
        from_account_id varchar(255) not null,
        recurring_payment_id varchar(255) not null unique,
        status varchar(255) not null check (status in ('ACTIVE','PAUSED','CANCELLED','COMPLETED')),
        to_account_id varchar(255) not null,
        primary key (id)
    )
2025-06-15 21:17:07 [main] DEBUG org.hibernate.SQL - 
    create table transactions (
        amount numeric(19,2) not null,
        created_at timestamp(6) not null,
        id bigint generated by default as identity,
        processed_at timestamp(6),
        customer_id varchar(255) not null,
        description varchar(255),
        from_account_id varchar(255) not null,
        recurring_payment_id varchar(255),
        status varchar(255) not null check (status in ('PENDING','COMPLETED','FAILED','CANCELLED')),
        to_account_id varchar(255) not null,
        transaction_id varchar(255) not null unique,
        transaction_type varchar(255) not null check (transaction_type in ('IMMEDIATE','RECURRING')),
        primary key (id)
    )
2025-06-15 21:17:07 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-15 21:17:07 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-15 21:17:07 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-15 21:17:07 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-06-15 21:17:07 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 31 mappings in 'requestMappingHandlerMapping'
2025-06-15 21:17:07 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**] in 'resourceHandlerMapping'
2025-06-15 21:17:07 [main] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 0 @ExceptionHandler, 1 ResponseBodyAdvice
2025-06-15 21:17:07 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-06-15 21:17:07 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
2025-06-15 21:17:07 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-15 21:17:07 [main] DEBUG org.hibernate.SQL - 
    drop table if exists accounts cascade 
2025-06-15 21:17:07 [main] DEBUG org.hibernate.SQL - 
    drop table if exists recurring_payments cascade 
2025-06-15 21:17:07 [main] DEBUG org.hibernate.SQL - 
    drop table if exists transactions cascade 
2025-06-15 21:17:07 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-06-15 21:17:07 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-06-15 21:17:07 [main] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-06-15 21:17:07 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-06-15 21:17:07 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8080 was already in use.

Action:

Identify and stop the process that's listening on port 8080 or configure this application to listen on another port.

2025-06-15 21:17:22 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - GET "/actuator/health", parameters={}
2025-06-15 21:17:22 [http-nio-8080-exec-2] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Using 'application/vnd.spring-boot.actuator.v3+json', given [*/*] and supported [application/vnd.spring-boot.actuator.v3+json, application/vnd.spring-boot.actuator.v2+json, application/json]
2025-06-15 21:17:22 [http-nio-8080-exec-2] DEBUG o.s.w.s.m.m.a.HttpEntityMethodProcessor - Writing [org.springframework.boot.actuate.health.SystemHealth@1f11dec]
2025-06-15 21:17:22 [http-nio-8080-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-15 21:17:41 [scheduling-1] DEBUG c.t.service.RecurringPaymentService - Processing due recurring payments
2025-06-15 21:17:41 [scheduling-1] DEBUG org.hibernate.SQL - 
    /* SELECT
        rp 
    FROM
        RecurringPayment rp 
    WHERE
        rp.status = :status 
        AND rp.nextPaymentDate <= :date */ select
            r1_0.id,
            r1_0.amount,
            r1_0.created_at,
            r1_0.customer_id,
            r1_0.description,
            r1_0.end_date,
            r1_0.frequency,
            r1_0.from_account_id,
            r1_0.last_executed_at,
            r1_0.next_payment_date,
            r1_0.recurring_payment_id,
            r1_0.start_date,
            r1_0.status,
            r1_0.to_account_id,
            r1_0.updated_at 
        from
            recurring_payments r1_0 
        where
            r1_0.status=? 
            and r1_0.next_payment_date<=?
2025-06-15 21:17:41 [scheduling-1] INFO  c.t.service.RecurringPaymentService - Found 0 due recurring payments
2025-06-15 21:18:41 [scheduling-1] DEBUG c.t.service.RecurringPaymentService - Processing due recurring payments
2025-06-15 21:18:41 [scheduling-1] DEBUG org.hibernate.SQL - 
    /* SELECT
        rp 
    FROM
        RecurringPayment rp 
    WHERE
        rp.status = :status 
        AND rp.nextPaymentDate <= :date */ select
            r1_0.id,
            r1_0.amount,
            r1_0.created_at,
            r1_0.customer_id,
            r1_0.description,
            r1_0.end_date,
            r1_0.frequency,
            r1_0.from_account_id,
            r1_0.last_executed_at,
            r1_0.next_payment_date,
            r1_0.recurring_payment_id,
            r1_0.start_date,
            r1_0.status,
            r1_0.to_account_id,
            r1_0.updated_at 
        from
            recurring_payments r1_0 
        where
            r1_0.status=? 
            and r1_0.next_payment_date<=?
2025-06-15 21:18:41 [scheduling-1] INFO  c.t.service.RecurringPaymentService - Found 0 due recurring payments
2025-06-15 21:19:41 [scheduling-1] DEBUG c.t.service.RecurringPaymentService - Processing due recurring payments
2025-06-15 21:19:41 [scheduling-1] DEBUG org.hibernate.SQL - 
    /* SELECT
        rp 
    FROM
        RecurringPayment rp 
    WHERE
        rp.status = :status 
        AND rp.nextPaymentDate <= :date */ select
            r1_0.id,
            r1_0.amount,
            r1_0.created_at,
            r1_0.customer_id,
            r1_0.description,
            r1_0.end_date,
            r1_0.frequency,
            r1_0.from_account_id,
            r1_0.last_executed_at,
            r1_0.next_payment_date,
            r1_0.recurring_payment_id,
            r1_0.start_date,
            r1_0.status,
            r1_0.to_account_id,
            r1_0.updated_at 
        from
            recurring_payments r1_0 
        where
            r1_0.status=? 
            and r1_0.next_payment_date<=?
2025-06-15 21:19:41 [scheduling-1] INFO  c.t.service.RecurringPaymentService - Found 0 due recurring payments
2025-06-15 21:20:41 [scheduling-1] DEBUG c.t.service.RecurringPaymentService - Processing due recurring payments
2025-06-15 21:20:41 [scheduling-1] DEBUG org.hibernate.SQL - 
    /* SELECT
        rp 
    FROM
        RecurringPayment rp 
    WHERE
        rp.status = :status 
        AND rp.nextPaymentDate <= :date */ select
            r1_0.id,
            r1_0.amount,
            r1_0.created_at,
            r1_0.customer_id,
            r1_0.description,
            r1_0.end_date,
            r1_0.frequency,
            r1_0.from_account_id,
            r1_0.last_executed_at,
            r1_0.next_payment_date,
            r1_0.recurring_payment_id,
            r1_0.start_date,
            r1_0.status,
            r1_0.to_account_id,
            r1_0.updated_at 
        from
            recurring_payments r1_0 
        where
            r1_0.status=? 
            and r1_0.next_payment_date<=?
2025-06-15 21:20:41 [scheduling-1] INFO  c.t.service.RecurringPaymentService - Found 0 due recurring payments
2025-06-15 21:21:41 [scheduling-1] DEBUG c.t.service.RecurringPaymentService - Processing due recurring payments
2025-06-15 21:21:41 [scheduling-1] DEBUG org.hibernate.SQL - 
    /* SELECT
        rp 
    FROM
        RecurringPayment rp 
    WHERE
        rp.status = :status 
        AND rp.nextPaymentDate <= :date */ select
            r1_0.id,
            r1_0.amount,
            r1_0.created_at,
            r1_0.customer_id,
            r1_0.description,
            r1_0.end_date,
            r1_0.frequency,
            r1_0.from_account_id,
            r1_0.last_executed_at,
            r1_0.next_payment_date,
            r1_0.recurring_payment_id,
            r1_0.start_date,
            r1_0.status,
            r1_0.to_account_id,
            r1_0.updated_at 
        from
            recurring_payments r1_0 
        where
            r1_0.status=? 
            and r1_0.next_payment_date<=?
2025-06-15 21:21:41 [scheduling-1] INFO  c.t.service.RecurringPaymentService - Found 0 due recurring payments
2025-06-15 21:22:41 [scheduling-1] DEBUG c.t.service.RecurringPaymentService - Processing due recurring payments
2025-06-15 21:22:41 [scheduling-1] DEBUG org.hibernate.SQL - 
    /* SELECT
        rp 
    FROM
        RecurringPayment rp 
    WHERE
        rp.status = :status 
        AND rp.nextPaymentDate <= :date */ select
            r1_0.id,
            r1_0.amount,
            r1_0.created_at,
            r1_0.customer_id,
            r1_0.description,
            r1_0.end_date,
            r1_0.frequency,
            r1_0.from_account_id,
            r1_0.last_executed_at,
            r1_0.next_payment_date,
            r1_0.recurring_payment_id,
            r1_0.start_date,
            r1_0.status,
            r1_0.to_account_id,
            r1_0.updated_at 
        from
            recurring_payments r1_0 
        where
            r1_0.status=? 
            and r1_0.next_payment_date<=?
2025-06-15 21:22:41 [scheduling-1] INFO  c.t.service.RecurringPaymentService - Found 0 due recurring payments
2025-06-15 21:23:41 [scheduling-1] DEBUG c.t.service.RecurringPaymentService - Processing due recurring payments
2025-06-15 21:23:41 [scheduling-1] DEBUG org.hibernate.SQL - 
    /* SELECT
        rp 
    FROM
        RecurringPayment rp 
    WHERE
        rp.status = :status 
        AND rp.nextPaymentDate <= :date */ select
            r1_0.id,
            r1_0.amount,
            r1_0.created_at,
            r1_0.customer_id,
            r1_0.description,
            r1_0.end_date,
            r1_0.frequency,
            r1_0.from_account_id,
            r1_0.last_executed_at,
            r1_0.next_payment_date,
            r1_0.recurring_payment_id,
            r1_0.start_date,
            r1_0.status,
            r1_0.to_account_id,
            r1_0.updated_at 
        from
            recurring_payments r1_0 
        where
            r1_0.status=? 
            and r1_0.next_payment_date<=?
2025-06-15 21:23:41 [scheduling-1] INFO  c.t.service.RecurringPaymentService - Found 0 due recurring payments
2025-06-15 21:24:41 [scheduling-1] DEBUG c.t.service.RecurringPaymentService - Processing due recurring payments
2025-06-15 21:24:41 [scheduling-1] DEBUG org.hibernate.SQL - 
    /* SELECT
        rp 
    FROM
        RecurringPayment rp 
    WHERE
        rp.status = :status 
        AND rp.nextPaymentDate <= :date */ select
            r1_0.id,
            r1_0.amount,
            r1_0.created_at,
            r1_0.customer_id,
            r1_0.description,
            r1_0.end_date,
            r1_0.frequency,
            r1_0.from_account_id,
            r1_0.last_executed_at,
            r1_0.next_payment_date,
            r1_0.recurring_payment_id,
            r1_0.start_date,
            r1_0.status,
            r1_0.to_account_id,
            r1_0.updated_at 
        from
            recurring_payments r1_0 
        where
            r1_0.status=? 
            and r1_0.next_payment_date<=?
2025-06-15 21:24:41 [scheduling-1] INFO  c.t.service.RecurringPaymentService - Found 0 due recurring payments
2025-06-15 21:25:41 [scheduling-1] DEBUG c.t.service.RecurringPaymentService - Processing due recurring payments
2025-06-15 21:25:41 [scheduling-1] DEBUG org.hibernate.SQL - 
    /* SELECT
        rp 
    FROM
        RecurringPayment rp 
    WHERE
        rp.status = :status 
        AND rp.nextPaymentDate <= :date */ select
            r1_0.id,
            r1_0.amount,
            r1_0.created_at,
            r1_0.customer_id,
            r1_0.description,
            r1_0.end_date,
            r1_0.frequency,
            r1_0.from_account_id,
            r1_0.last_executed_at,
            r1_0.next_payment_date,
            r1_0.recurring_payment_id,
            r1_0.start_date,
            r1_0.status,
            r1_0.to_account_id,
            r1_0.updated_at 
        from
            recurring_payments r1_0 
        where
            r1_0.status=? 
            and r1_0.next_payment_date<=?
2025-06-15 21:25:41 [scheduling-1] INFO  c.t.service.RecurringPaymentService - Found 0 due recurring payments
2025-06-15 21:26:41 [scheduling-1] DEBUG c.t.service.RecurringPaymentService - Processing due recurring payments
2025-06-15 21:26:41 [scheduling-1] DEBUG org.hibernate.SQL - 
    /* SELECT
        rp 
    FROM
        RecurringPayment rp 
    WHERE
        rp.status = :status 
        AND rp.nextPaymentDate <= :date */ select
            r1_0.id,
            r1_0.amount,
            r1_0.created_at,
            r1_0.customer_id,
            r1_0.description,
            r1_0.end_date,
            r1_0.frequency,
            r1_0.from_account_id,
            r1_0.last_executed_at,
            r1_0.next_payment_date,
            r1_0.recurring_payment_id,
            r1_0.start_date,
            r1_0.status,
            r1_0.to_account_id,
            r1_0.updated_at 
        from
            recurring_payments r1_0 
        where
            r1_0.status=? 
            and r1_0.next_payment_date<=?
2025-06-15 21:26:41 [scheduling-1] INFO  c.t.service.RecurringPaymentService - Found 0 due recurring payments
