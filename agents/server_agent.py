#!/usr/bin/env python3
"""
Server Agent for Money Transfer System
Uses MCP tools to handle transfer operations and communicates with Client Agent
"""

import asyncio
import json
import logging
import os
import subprocess
from typing import Any, Dict, List, Optional
from datetime import datetime

import openai
from pydantic import BaseModel

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class TransferRequest(BaseModel):
    customer_id: str
    request_text: str
    context: Optional[Dict[str, Any]] = None

class TransferResponse(BaseModel):
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None
    suggestions: Optional[List[str]] = None

class ServerAgent:
    def __init__(self):
        self.openai_client = openai.OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
        self.mcp_process = None
        logger.info("Server Agent initialized with OpenAI integration")

    async def start_mcp_server(self):
        """Start the MCP server process"""
        try:
            logger.info("Starting MCP server...")
            self.mcp_process = await asyncio.create_subprocess_exec(
                "python", "../mcp-server/server.py",
                stdin=asyncio.subprocess.PIPE,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            logger.info("MCP server started successfully")
            
        except Exception as e:
            logger.error(f"Failed to start MCP server: {e}")
            raise

    async def call_mcp_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Call MCP tool and return response"""
        logger.info(f"Calling MCP tool: {tool_name} with args: {arguments}")
        
        try:
            # Prepare MCP request
            mcp_request = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "tools/call",
                "params": {
                    "name": tool_name,
                    "arguments": arguments
                }
            }
            
            # Send request to MCP server
            request_json = json.dumps(mcp_request) + "\n"
            self.mcp_process.stdin.write(request_json.encode())
            await self.mcp_process.stdin.drain()
            
            # Read response
            response_line = await self.mcp_process.stdout.readline()
            response = json.loads(response_line.decode())
            
            logger.info(f"MCP tool response: {response}")
            return response
            
        except Exception as e:
            logger.error(f"Error calling MCP tool: {e}")
            return {"error": str(e)}

    async def analyze_request_with_llm(self, request: TransferRequest) -> Dict[str, Any]:
        """Analyze user request using LLM to extract intent and parameters"""
        logger.info(f"Analyzing request with LLM: {request.request_text}")
        
        system_prompt = """
You are an intelligent banking assistant that analyzes customer requests for money transfers and account operations.

Your job is to:
1. Understand the customer's intent
2. Extract relevant parameters
3. Identify missing information
4. Suggest next steps

Available operations:
- Create account (checking/savings with initial deposit)
- Check account balance
- Execute immediate transfer
- Create recurring payment (weekly/bi-weekly/monthly)
- View transaction history
- Manage recurring payments (pause/resume/cancel)

Respond in JSON format with:
{
    "intent": "operation_name",
    "confidence": 0.0-1.0,
    "parameters": {extracted_parameters},
    "missing_parameters": [list_of_missing_required_params],
    "suggestions": [list_of_suggestions_for_user],
    "requires_account_list": boolean,
    "reasoning": "explanation_of_analysis"
}
"""
        
        try:
            response = self.openai_client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": f"Customer ID: {request.customer_id}\nRequest: {request.request_text}"}
                ],
                temperature=0.1
            )
            
            analysis = json.loads(response.choices[0].message.content)
            logger.info(f"LLM analysis result: {analysis}")
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing request with LLM: {e}")
            return {
                "intent": "unknown",
                "confidence": 0.0,
                "parameters": {},
                "missing_parameters": [],
                "suggestions": ["I couldn't understand your request. Please try again."],
                "requires_account_list": False,
                "reasoning": f"Error: {str(e)}"
            }

    async def get_customer_accounts(self, customer_id: str) -> List[Dict[str, Any]]:
        """Get customer accounts for context"""
        logger.info(f"Getting accounts for customer: {customer_id}")
        
        try:
            response = await self.call_mcp_tool("get_accounts", {"customerId": customer_id})
            
            if "error" in response:
                return []
                
            # Parse the response to extract account information
            # This is a simplified version - in practice, you'd parse the actual response
            return []
            
        except Exception as e:
            logger.error(f"Error getting customer accounts: {e}")
            return []

    async def execute_operation(self, intent: str, parameters: Dict[str, Any], customer_id: str) -> Dict[str, Any]:
        """Execute the identified operation using MCP tools"""
        logger.info(f"Executing operation: {intent} with parameters: {parameters}")
        
        try:
            if intent == "create_account":
                return await self.call_mcp_tool("create_account", {
                    "customerId": customer_id,
                    **parameters
                })
                
            elif intent == "get_accounts":
                return await self.call_mcp_tool("get_accounts", {"customerId": customer_id})
                
            elif intent == "get_balance":
                return await self.call_mcp_tool("get_account_balance", parameters)
                
            elif intent == "check_funds":
                return await self.call_mcp_tool("check_sufficient_funds", parameters)
                
            elif intent == "immediate_transfer":
                return await self.call_mcp_tool("execute_immediate_transfer", {
                    "customerId": customer_id,
                    **parameters
                })
                
            elif intent == "create_recurring":
                return await self.call_mcp_tool("create_recurring_payment", {
                    "customerId": customer_id,
                    **parameters
                })
                
            elif intent == "get_transactions":
                return await self.call_mcp_tool("get_transactions", {
                    "customerId": customer_id,
                    **parameters
                })
                
            elif intent == "get_recurring_payments":
                return await self.call_mcp_tool("get_recurring_payments", {
                    "customerId": customer_id,
                    **parameters
                })
                
            elif intent == "manage_recurring":
                return await self.call_mcp_tool("manage_recurring_payment", parameters)
                
            else:
                return {"error": f"Unknown operation: {intent}"}
                
        except Exception as e:
            logger.error(f"Error executing operation: {e}")
            return {"error": str(e)}

    async def generate_response_with_llm(self, analysis: Dict[str, Any], operation_result: Dict[str, Any], 
                                       original_request: str) -> str:
        """Generate natural language response using LLM"""
        logger.info("Generating response with LLM")
        
        system_prompt = """
You are a helpful banking assistant. Generate a natural, conversational response based on the operation result.

Guidelines:
- Be friendly and professional
- Explain what happened clearly
- If there were errors, explain them in simple terms
- If information is missing, ask for it politely
- Provide helpful suggestions when appropriate
- Use emojis sparingly but appropriately
"""
        
        context = f"""
Original request: {original_request}
Analysis: {json.dumps(analysis, indent=2)}
Operation result: {json.dumps(operation_result, indent=2)}
"""
        
        try:
            response = self.openai_client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": context}
                ],
                temperature=0.3
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            logger.error(f"Error generating response: {e}")
            return "I apologize, but I encountered an error processing your request. Please try again."

    async def process_request(self, request: TransferRequest) -> TransferResponse:
        """Main method to process transfer requests"""
        logger.info(f"Processing request from customer {request.customer_id}: {request.request_text}")
        
        try:
            # Step 1: Analyze request with LLM
            analysis = await self.analyze_request_with_llm(request)
            
            # Step 2: Get customer context if needed
            if analysis.get("requires_account_list", False):
                accounts = await self.get_customer_accounts(request.customer_id)
                analysis["customer_accounts"] = accounts
            
            # Step 3: Check if we have all required parameters
            if analysis.get("missing_parameters"):
                suggestions = analysis.get("suggestions", [])
                return TransferResponse(
                    success=False,
                    message="I need more information to help you.",
                    suggestions=suggestions
                )
            
            # Step 4: Execute the operation
            operation_result = await self.execute_operation(
                analysis["intent"], 
                analysis["parameters"], 
                request.customer_id
            )
            
            # Step 5: Generate natural language response
            response_text = await self.generate_response_with_llm(
                analysis, operation_result, request.request_text
            )
            
            # Step 6: Return structured response
            success = "error" not in operation_result
            return TransferResponse(
                success=success,
                message=response_text,
                data=operation_result if success else None
            )
            
        except Exception as e:
            logger.error(f"Error processing request: {e}")
            return TransferResponse(
                success=False,
                message="I apologize, but I encountered an error processing your request. Please try again.",
                data={"error": str(e)}
            )

    async def start(self):
        """Start the server agent"""
        logger.info("Starting Server Agent...")
        
        # Start MCP server
        await self.start_mcp_server()
        
        # Wait a bit for MCP server to initialize
        await asyncio.sleep(2)
        
        logger.info("Server Agent ready to process requests")

    async def stop(self):
        """Stop the server agent"""
        logger.info("Stopping Server Agent...")
        
        if self.mcp_process:
            self.mcp_process.terminate()
            await self.mcp_process.wait()
            
        logger.info("Server Agent stopped")

async def main():
    """Main entry point for testing"""
    agent = ServerAgent()
    
    try:
        await agent.start()
        
        # Test request
        test_request = TransferRequest(
            customer_id="CUST001",
            request_text="I want to transfer $100 from my checking account to my savings account"
        )
        
        response = await agent.process_request(test_request)
        print(f"Response: {response}")
        
    except KeyboardInterrupt:
        logger.info("Received interrupt signal")
    finally:
        await agent.stop()

if __name__ == "__main__":
    asyncio.run(main())
