#!/usr/bin/env python3
"""
Client Agent for Money Transfer System
Handles user interactions and communicates with Server Agent via A2A protocol
"""

import asyncio
import json
import logging
import os
import websockets
from typing import Any, Dict, List, Optional
from datetime import datetime

import openai
from pydantic import BaseModel

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class UserSession(BaseModel):
    user_id: str
    conversation_history: List[Dict[str, str]] = []
    context: Dict[str, Any] = {}

class ClientAgent:
    def __init__(self):
        self.openai_client = openai.OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
        self.sessions: Dict[str, UserSession] = {}
        self.server_agent_url = "ws://localhost:8765"
        logger.info("Client Agent initialized with OpenAI integration")

    def get_or_create_session(self, user_id: str) -> UserSession:
        """Get or create user session"""
        if user_id not in self.sessions:
            self.sessions[user_id] = UserSession(user_id=user_id)
        return self.sessions[user_id]

    async def understand_user_intent(self, user_input: str, session: UserSession) -> Dict[str, Any]:
        """Use LLM to understand user intent and provide conversational guidance"""
        logger.info(f"Understanding user intent: {user_input}")
        
        conversation_context = "\n".join([
            f"{msg['role']}: {msg['content']}" 
            for msg in session.conversation_history[-5:]  # Last 5 messages
        ])
        
        system_prompt = """
You are a friendly banking assistant helping users with money transfers and account management.

Your role is to:
1. Understand what the user wants to do
2. Guide them through the process conversationally
3. Ask for missing information in a natural way
4. Provide helpful suggestions and options
5. Explain banking concepts when needed
6. No login or authentication is required - users can perform all operations directly

Available services:
- Create new accounts (checking/savings)
- Check account balances
- Transfer money between accounts (immediate or scheduled)
- Set up recurring payments (weekly/bi-weekly/monthly)
- View transaction history
- Manage existing recurring payments

Respond in JSON format:
{
    "intent": "detected_intent",
    "confidence": 0.0-1.0,
    "response_type": "question|confirmation|information|error",
    "message": "conversational_response_to_user",
    "needs_clarification": boolean,
    "clarification_questions": [list_of_questions],
    "ready_for_execution": boolean,
    "extracted_params": {parameters_found},
    "suggestions": [helpful_suggestions]
}
"""
        
        try:
            response = self.openai_client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": f"Conversation history:\n{conversation_context}\n\nCurrent message: {user_input}\n\nNote: No login required - user can perform all banking operations directly."}
                ],
                temperature=0.3
            )
            
            analysis = json.loads(response.choices[0].message.content)
            logger.info(f"Intent analysis: {analysis}")
            return analysis
            
        except Exception as e:
            logger.error(f"Error understanding user intent: {e}")
            return {
                "intent": "unknown",
                "confidence": 0.0,
                "response_type": "error",
                "message": "I'm having trouble understanding your request. Could you please rephrase it?",
                "needs_clarification": True,
                "clarification_questions": ["What would you like to do with your accounts today?"],
                "ready_for_execution": False,
                "extracted_params": {},
                "suggestions": ["Check account balance", "Transfer money", "View transaction history"]
            }

    async def communicate_with_server_agent(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """Communicate with Server Agent using A2A protocol (WebSocket)"""
        logger.info(f"Communicating with server agent: {request_data}")

        try:
            import websockets

            # Connect to Server Agent WebSocket
            async with websockets.connect(self.server_agent_url) as websocket:
                # Send request to Server Agent
                await websocket.send(json.dumps(request_data))
                logger.info(f"Sent request to Server Agent: {request_data}")

                # Wait for response
                response_message = await websocket.recv()
                response_data = json.loads(response_message)
                logger.info(f"Received response from Server Agent: {response_data}")

                return response_data

        except websockets.exceptions.ConnectionRefused:
            logger.error("Could not connect to Server Agent. Is it running?")
            return {
                "success": False,
                "message": "I'm having trouble connecting to the banking system. Please make sure all services are running.",
                "error": "Server Agent connection refused"
            }
        except Exception as e:
            logger.error(f"Error communicating with server agent: {e}")
            return {
                "success": False,
                "message": "I'm having trouble processing your request right now. Please try again in a moment.",
                "error": str(e)
            }

    async def generate_conversational_response(self, user_input: str, intent_analysis: Dict[str, Any], 
                                             server_response: Optional[Dict[str, Any]] = None) -> str:
        """Generate natural conversational response"""
        logger.info("Generating conversational response")
        
        if server_response and server_response.get("success"):
            # Successful operation - enhance the server response
            base_message = server_response.get("message", "")
            
            # Add conversational elements
            if "transfer" in user_input.lower() and "completed" in base_message:
                return f"Great! {base_message}\n\nIs there anything else I can help you with today?"
            elif "balance" in user_input.lower():
                return f"{base_message}\n\nWould you like to make a transfer or check another account?"
            elif "account" in user_input.lower() and "created" in base_message:
                return f"Excellent! {base_message}\n\nYour new account is ready to use. Would you like to make a deposit or transfer?"
            else:
                return f"{base_message}\n\nHow else can I assist you?"
                
        elif server_response and not server_response.get("success"):
            # Error from server
            error_message = server_response.get("message", "Something went wrong.")
            suggestions = server_response.get("suggestions", [])
            
            response = f"I apologize, but {error_message.lower()}"
            if suggestions:
                response += f"\n\nHere are some things you can try:\n" + "\n".join([f"• {s}" for s in suggestions])
            
            return response
            
        else:
            # Use intent analysis response
            message = intent_analysis.get("message", "I'm here to help with your banking needs.")
            
            if intent_analysis.get("needs_clarification"):
                questions = intent_analysis.get("clarification_questions", [])
                if questions:
                    message += f"\n\n{questions[0]}"
                    
            suggestions = intent_analysis.get("suggestions", [])
            if suggestions:
                message += f"\n\nSome things I can help with:\n" + "\n".join([f"• {s}" for s in suggestions])
                
            return message

    async def process_user_input(self, user_id: str, user_input: str) -> str:
        """Main method to process user input"""
        logger.info(f"Processing input from user {user_id}: {user_input}")

        try:
            # Get or create session
            session = self.get_or_create_session(user_id)
            
            # Add user input to conversation history
            session.conversation_history.append({
                "role": "user",
                "content": user_input,
                "timestamp": datetime.now().isoformat()
            })
            
            # Step 1: Understand user intent
            intent_analysis = await self.understand_user_intent(user_input, session)
            
            # Step 2: Check if we need to execute an operation
            server_response = None
            if intent_analysis.get("ready_for_execution", False):
                # Prepare request for server agent
                request_data = {
                    "user_id": user_id,
                    "intent": intent_analysis["intent"],
                    "parameters": intent_analysis.get("extracted_params", {}),
                    "request_text": user_input
                }
                
                # Communicate with server agent
                server_response = await self.communicate_with_server_agent(request_data)
            
            # Step 3: Generate conversational response
            response_text = await self.generate_conversational_response(
                user_input, intent_analysis, server_response
            )
            
            # Add assistant response to conversation history
            session.conversation_history.append({
                "role": "assistant",
                "content": response_text,
                "timestamp": datetime.now().isoformat()
            })
            
            # Keep conversation history manageable
            if len(session.conversation_history) > 20:
                session.conversation_history = session.conversation_history[-20:]
            
            logger.info(f"Generated response: {response_text}")
            return response_text
            
        except Exception as e:
            logger.error(f"Error processing user input: {e}")
            return "I apologize, but I'm having trouble processing your request right now. Please try again in a moment."

    async def start_conversation(self, user_id: str) -> str:
        """Start a new conversation with greeting"""
        logger.info(f"Starting conversation for user: {user_id}")

        greeting = f"""Hello! 👋 I'm your personal banking assistant.

I can help you with:
• Checking account balances
• Transferring money between accounts
• Setting up recurring payments
• Viewing transaction history
• Creating new accounts

No login required - just tell me what you'd like to do!

What would you like to do today?"""

        # Initialize session
        session = self.get_or_create_session(user_id)
        session.conversation_history.append({
            "role": "assistant",
            "content": greeting,
            "timestamp": datetime.now().isoformat()
        })

        return greeting

    def get_conversation_history(self, user_id: str) -> List[Dict[str, str]]:
        """Get conversation history for a user"""
        session = self.sessions.get(user_id)
        if session:
            return session.conversation_history
        return []

    async def clear_session(self, user_id: str):
        """Clear user session"""
        if user_id in self.sessions:
            del self.sessions[user_id]
        logger.info(f"Cleared session for user: {user_id}")

    async def start_http_server(self):
        """Start HTTP server for frontend communication"""
        from fastapi import FastAPI, HTTPException
        from fastapi.middleware.cors import CORSMiddleware
        import uvicorn

        app = FastAPI(title="Client Agent API")

        # Add CORS middleware
        app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )

        @app.post("/chat")
        async def chat_endpoint(request: dict):
            """Chat endpoint for frontend"""
            try:
                user_id = request.get("customer_id", "DEFAULT_USER")  # Keep same key for compatibility
                message = request.get("message", "")

                if not message:
                    raise HTTPException(status_code=400, detail="Message is required")

                response = await self.process_user_input(user_id, message)

                return {
                    "success": True,
                    "response": response,
                    "user_id": user_id
                }

            except Exception as e:
                logger.error(f"Error in chat endpoint: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        @app.post("/start_conversation")
        async def start_conversation_endpoint(request: dict):
            """Start conversation endpoint"""
            try:
                user_id = request.get("customer_id", "DEFAULT_USER")  # Keep same key for compatibility
                greeting = await self.start_conversation(user_id)

                return {
                    "success": True,
                    "greeting": greeting,
                    "user_id": user_id
                }

            except Exception as e:
                logger.error(f"Error starting conversation: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        @app.get("/history/{customer_id}")
        async def get_history_endpoint(customer_id: str):
            """Get conversation history"""
            try:
                history = self.get_conversation_history(customer_id)
                return {
                    "success": True,
                    "history": history,
                    "customer_id": customer_id
                }

            except Exception as e:
                logger.error(f"Error getting history: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        @app.delete("/session/{customer_id}")
        async def clear_session_endpoint(customer_id: str):
            """Clear session endpoint"""
            try:
                await self.clear_session(customer_id)
                return {
                    "success": True,
                    "message": f"Session cleared for {customer_id}"
                }

            except Exception as e:
                logger.error(f"Error clearing session: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        # Store the app instance
        self.app = app

        # Start the server
        config = uvicorn.Config(app, host="0.0.0.0", port=8766, log_level="info")
        server = uvicorn.Server(config)

        logger.info("Starting Client Agent HTTP server on port 8766")
        await server.serve()

async def main():
    """Main entry point - starts the Client Agent HTTP server"""
    agent = ClientAgent()

    try:
        logger.info("Starting Client Agent...")
        await agent.start_http_server()
    except KeyboardInterrupt:
        logger.info("Client Agent stopped by user")
    except Exception as e:
        logger.error(f"Error starting Client Agent: {e}")

if __name__ == "__main__":
    asyncio.run(main())
