#!/usr/bin/env python3
"""
Client Agent for Money Transfer System
Handles user interactions and communicates with Server Agent via A2A protocol
"""

import asyncio
import json
import logging
import os
import websockets
from typing import Any, Dict, List, Optional
from datetime import datetime

import openai
from google.cloud import aiplatform
from pydantic import BaseModel

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class UserSession(BaseModel):
    customer_id: str
    conversation_history: List[Dict[str, str]] = []
    context: Dict[str, Any] = {}

class ClientAgent:
    def __init__(self):
        self.openai_client = openai.OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
        self.sessions: Dict[str, UserSession] = {}
        self.server_agent_url = "ws://localhost:8765"
        self.setup_google_adk()
        
    def setup_google_adk(self):
        """Initialize Google ADK"""
        try:
            # Initialize Google Cloud AI Platform
            project_id = os.getenv("GOOGLE_CLOUD_PROJECT_ID", "transfer-system")
            location = os.getenv("GOOGLE_CLOUD_LOCATION", "us-central1")
            
            aiplatform.init(project=project_id, location=location)
            logger.info("Google ADK initialized successfully")
            
        except Exception as e:
            logger.warning(f"Google ADK initialization failed: {e}. Using OpenAI only.")

    def get_or_create_session(self, customer_id: str) -> UserSession:
        """Get or create user session"""
        if customer_id not in self.sessions:
            self.sessions[customer_id] = UserSession(customer_id=customer_id)
        return self.sessions[customer_id]

    async def understand_user_intent(self, user_input: str, session: UserSession) -> Dict[str, Any]:
        """Use LLM to understand user intent and provide conversational guidance"""
        logger.info(f"Understanding user intent: {user_input}")
        
        conversation_context = "\n".join([
            f"{msg['role']}: {msg['content']}" 
            for msg in session.conversation_history[-5:]  # Last 5 messages
        ])
        
        system_prompt = """
You are a friendly banking assistant helping customers with money transfers and account management.

Your role is to:
1. Understand what the customer wants to do
2. Guide them through the process conversationally
3. Ask for missing information in a natural way
4. Provide helpful suggestions and options
5. Explain banking concepts when needed

Available services:
- Create new accounts (checking/savings)
- Check account balances
- Transfer money between accounts (immediate or scheduled)
- Set up recurring payments (weekly/bi-weekly/monthly)
- View transaction history
- Manage existing recurring payments

Respond in JSON format:
{
    "intent": "detected_intent",
    "confidence": 0.0-1.0,
    "response_type": "question|confirmation|information|error",
    "message": "conversational_response_to_user",
    "needs_clarification": boolean,
    "clarification_questions": [list_of_questions],
    "ready_for_execution": boolean,
    "extracted_params": {parameters_found},
    "suggestions": [helpful_suggestions]
}
"""
        
        try:
            response = self.openai_client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": f"Conversation history:\n{conversation_context}\n\nCurrent message: {user_input}"}
                ],
                temperature=0.3
            )
            
            analysis = json.loads(response.choices[0].message.content)
            logger.info(f"Intent analysis: {analysis}")
            return analysis
            
        except Exception as e:
            logger.error(f"Error understanding user intent: {e}")
            return {
                "intent": "unknown",
                "confidence": 0.0,
                "response_type": "error",
                "message": "I'm having trouble understanding your request. Could you please rephrase it?",
                "needs_clarification": True,
                "clarification_questions": ["What would you like to do with your accounts today?"],
                "ready_for_execution": False,
                "extracted_params": {},
                "suggestions": ["Check account balance", "Transfer money", "View transaction history"]
            }

    async def communicate_with_server_agent(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """Communicate with Server Agent using A2A protocol (WebSocket)"""
        logger.info(f"Communicating with server agent: {request_data}")
        
        try:
            # For now, we'll simulate the communication
            # In a real implementation, this would use WebSocket or HTTP to communicate
            # with the actual server agent
            
            # Simulate server agent processing
            await asyncio.sleep(0.5)  # Simulate network delay
            
            # Mock response based on intent
            intent = request_data.get("intent", "unknown")
            
            if intent == "get_accounts":
                return {
                    "success": True,
                    "message": "Here are your accounts:\n• My Checking (CHK-********) - $1,250.00\n• My Savings (SAV-********) - $5,000.00",
                    "data": {
                        "accounts": [
                            {"accountId": "CHK-********", "accountName": "My Checking", "accountType": "CHECKING", "balance": 1250.00},
                            {"accountId": "SAV-********", "accountName": "My Savings", "accountType": "SAVINGS", "balance": 5000.00}
                        ]
                    }
                }
            elif intent == "immediate_transfer":
                return {
                    "success": True,
                    "message": "✅ Transfer completed successfully!\nTransaction ID: TXN-ABC********9\nFrom: CHK-******** → To: SAV-********\nAmount: $100.00\nStatus: COMPLETED",
                    "data": {
                        "transactionId": "TXN-ABC********9",
                        "status": "COMPLETED"
                    }
                }
            elif intent == "get_balance":
                return {
                    "success": True,
                    "message": "Account Balance:\nMy Checking (CHK-********)\nBalance: $1,150.00",
                    "data": {
                        "balance": 1150.00
                    }
                }
            else:
                return {
                    "success": False,
                    "message": "I'm not sure how to help with that request. Could you please be more specific?",
                    "suggestions": ["Check account balance", "Transfer money", "View transaction history"]
                }
                
        except Exception as e:
            logger.error(f"Error communicating with server agent: {e}")
            return {
                "success": False,
                "message": "I'm having trouble processing your request right now. Please try again in a moment.",
                "error": str(e)
            }

    async def generate_conversational_response(self, user_input: str, intent_analysis: Dict[str, Any], 
                                             server_response: Optional[Dict[str, Any]] = None) -> str:
        """Generate natural conversational response"""
        logger.info("Generating conversational response")
        
        if server_response and server_response.get("success"):
            # Successful operation - enhance the server response
            base_message = server_response.get("message", "")
            
            # Add conversational elements
            if "transfer" in user_input.lower() and "completed" in base_message:
                return f"Great! {base_message}\n\nIs there anything else I can help you with today?"
            elif "balance" in user_input.lower():
                return f"{base_message}\n\nWould you like to make a transfer or check another account?"
            elif "account" in user_input.lower() and "created" in base_message:
                return f"Excellent! {base_message}\n\nYour new account is ready to use. Would you like to make a deposit or transfer?"
            else:
                return f"{base_message}\n\nHow else can I assist you?"
                
        elif server_response and not server_response.get("success"):
            # Error from server
            error_message = server_response.get("message", "Something went wrong.")
            suggestions = server_response.get("suggestions", [])
            
            response = f"I apologize, but {error_message.lower()}"
            if suggestions:
                response += f"\n\nHere are some things you can try:\n" + "\n".join([f"• {s}" for s in suggestions])
            
            return response
            
        else:
            # Use intent analysis response
            message = intent_analysis.get("message", "I'm here to help with your banking needs.")
            
            if intent_analysis.get("needs_clarification"):
                questions = intent_analysis.get("clarification_questions", [])
                if questions:
                    message += f"\n\n{questions[0]}"
                    
            suggestions = intent_analysis.get("suggestions", [])
            if suggestions:
                message += f"\n\nSome things I can help with:\n" + "\n".join([f"• {s}" for s in suggestions])
                
            return message

    async def process_user_input(self, customer_id: str, user_input: str) -> str:
        """Main method to process user input"""
        logger.info(f"Processing input from customer {customer_id}: {user_input}")
        
        try:
            # Get or create session
            session = self.get_or_create_session(customer_id)
            
            # Add user input to conversation history
            session.conversation_history.append({
                "role": "user",
                "content": user_input,
                "timestamp": datetime.now().isoformat()
            })
            
            # Step 1: Understand user intent
            intent_analysis = await self.understand_user_intent(user_input, session)
            
            # Step 2: Check if we need to execute an operation
            server_response = None
            if intent_analysis.get("ready_for_execution", False):
                # Prepare request for server agent
                request_data = {
                    "customer_id": customer_id,
                    "intent": intent_analysis["intent"],
                    "parameters": intent_analysis.get("extracted_params", {}),
                    "request_text": user_input
                }
                
                # Communicate with server agent
                server_response = await self.communicate_with_server_agent(request_data)
            
            # Step 3: Generate conversational response
            response_text = await self.generate_conversational_response(
                user_input, intent_analysis, server_response
            )
            
            # Add assistant response to conversation history
            session.conversation_history.append({
                "role": "assistant",
                "content": response_text,
                "timestamp": datetime.now().isoformat()
            })
            
            # Keep conversation history manageable
            if len(session.conversation_history) > 20:
                session.conversation_history = session.conversation_history[-20:]
            
            logger.info(f"Generated response: {response_text}")
            return response_text
            
        except Exception as e:
            logger.error(f"Error processing user input: {e}")
            return "I apologize, but I'm having trouble processing your request right now. Please try again in a moment."

    async def start_conversation(self, customer_id: str) -> str:
        """Start a new conversation with greeting"""
        logger.info(f"Starting conversation for customer: {customer_id}")
        
        greeting = f"""Hello! 👋 I'm your personal banking assistant.

I can help you with:
• Checking account balances
• Transferring money between accounts
• Setting up recurring payments
• Viewing transaction history
• Creating new accounts

What would you like to do today?"""
        
        # Initialize session
        session = self.get_or_create_session(customer_id)
        session.conversation_history.append({
            "role": "assistant",
            "content": greeting,
            "timestamp": datetime.now().isoformat()
        })
        
        return greeting

    def get_conversation_history(self, customer_id: str) -> List[Dict[str, str]]:
        """Get conversation history for a customer"""
        session = self.sessions.get(customer_id)
        if session:
            return session.conversation_history
        return []

    async def clear_session(self, customer_id: str):
        """Clear customer session"""
        if customer_id in self.sessions:
            del self.sessions[customer_id]
        logger.info(f"Cleared session for customer: {customer_id}")

async def main():
    """Main entry point for testing"""
    agent = ClientAgent()
    
    # Test conversation
    customer_id = "CUST001"
    
    print("=== Banking Assistant Test ===")
    print(await agent.start_conversation(customer_id))
    
    # Simulate conversation
    test_inputs = [
        "I want to check my account balance",
        "Can you show me all my accounts?",
        "I need to transfer $100 from checking to savings",
        "Thank you!"
    ]
    
    for user_input in test_inputs:
        print(f"\nUser: {user_input}")
        response = await agent.process_user_input(customer_id, user_input)
        print(f"Assistant: {response}")

if __name__ == "__main__":
    asyncio.run(main())
