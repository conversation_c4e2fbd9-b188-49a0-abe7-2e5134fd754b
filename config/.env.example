# OpenAI Configuration (Required)
OPENAI_API_KEY=********************************************************************************************************************************************************************

# Backend API Configuration
BACKEND_BASE_URL=http://localhost:8080/api

# Agent Communication Configuration
SERVER_AGENT_WEBSOCKET_URL=ws://localhost:8765
CLIENT_AGENT_PORT=8766

# Database Configuration (H2 in-memory for local development)
DATABASE_URL=jdbc:h2:mem:transferdb
DATABASE_USERNAME=sa
DATABASE_PASSWORD=password

# Logging Configuration
LOG_LEVEL=DEBUG
LOG_FILE=logs/transfer-system.log
