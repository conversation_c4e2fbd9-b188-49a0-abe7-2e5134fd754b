# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here

# Google Cloud Configuration
GOOGLE_CLOUD_PROJECT_ID=your_project_id
GOOGLE_CLOUD_LOCATION=us-central1
GOOGLE_APPLICATION_CREDENTIALS=path/to/service-account.json

# Backend API Configuration
BACKEND_BASE_URL=http://localhost:8080/api

# MCP Server Configuration
MCP_SERVER_HOST=localhost
MCP_SERVER_PORT=8765

# Database Configuration (for production)
DATABASE_URL=*******************************************
DATABASE_USERNAME=transfer_user
DATABASE_PASSWORD=transfer_password

# Logging Configuration
LOG_LEVEL=DEBUG
LOG_FILE=logs/transfer-system.log
