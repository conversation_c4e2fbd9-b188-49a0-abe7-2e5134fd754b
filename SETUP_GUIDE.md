# 🚀 Quick Setup Guide - Intelligent Money Transfer System

## ✅ Prerequisites Check

Before starting, ensure you have:

1. **Java 17+** installed
   ```bash
   java -version
   # Should show version 17 or higher
   ```

2. **Python 3.8+** installed
   ```bash
   python3 --version
   # Should show version 3.8 or higher
   ```

3. **OpenAI API Key** (Required)
   - Get your API key from: https://platform.openai.com/api-keys
   - You'll need this for the AI agents to work

## 🎯 Quick Start (5 minutes)

### Step 1: Set Environment Variable
```bash
# Replace with your actual OpenAI API key
export OPENAI_API_KEY=sk-your-openai-api-key-here
```

### Step 2: Make Scripts Executable
```bash
chmod +x scripts/*.sh
```

### Step 3: Start Everything
```bash
./scripts/start-all.sh
```

### Step 4: Open Your Browser
Go to: **http://localhost:8501**

## 🏗️ System Architecture

```
User Input (Natural Language)
        ↓
Streamlit Frontend (Port 8501)
        ↓ HTTP
Client Agent (Port 8766) - OpenAI LLM
        ↓ WebSocket A2A
Server Agent (Port 8765) - OpenAI LLM  
        ↓ MCP Protocol
MCP Server (Tools)
        ↓ REST API
Spring Boot Backend (Port 8080)
        ↓
H2 Database (In-Memory)
```

## 🎮 How to Use

1. **Open the frontend**: http://localhost:8501
2. **Start chatting** with natural language:
   - "Show me my account balances"
   - "Transfer $100 from checking to savings"
   - "Set up a monthly transfer of $200"
   - "What are my recent transactions?"

## 📊 Service Endpoints

| Service | URL | Purpose |
|---------|-----|---------|
| Frontend | http://localhost:8501 | Main user interface |
| Client Agent | http://localhost:8766 | AI chat API |
| Server Agent | ws://localhost:8765 | WebSocket server |
| Backend API | http://localhost:8080 | REST API |
| H2 Console | http://localhost:8080/h2-console | Database viewer |

## 🔧 Individual Service Startup

If you prefer to start services individually:

```bash
# Terminal 1: Backend
./scripts/start-backend.sh

# Terminal 2: MCP Server  
./scripts/start-mcp-server.sh

# Terminal 3: AI Agents
./scripts/start-agents.sh

# Terminal 4: Frontend
./scripts/start-frontend.sh
```

## 🐛 Troubleshooting

### Common Issues:

1. **"OPENAI_API_KEY not set"**
   ```bash
   export OPENAI_API_KEY=your-key-here
   ```

2. **Port already in use**
   ```bash
   # Check what's using the port
   lsof -i :8080  # or :8501, :8765, :8766
   
   # Kill the process if needed
   kill -9 <PID>
   ```

3. **Java version issues**
   ```bash
   # Check Java version
   java -version
   
   # Install Java 17 if needed (macOS)
   brew install openjdk@17
   ```

4. **Python dependencies**
   ```bash
   # If you get import errors, recreate virtual environments
   rm -rf */venv
   ./scripts/start-all.sh
   ```

### Service Health Checks:

```bash
# Backend health
curl http://localhost:8080/actuator/health

# Client Agent health  
curl http://localhost:8766/docs

# Check logs
tail -f logs/*.log
```

## 🎯 Testing the System

### Sample Conversations:

1. **Account Management**:
   - "What accounts do I have?"
   - "Show my checking account balance"
   - "Create a new savings account with $500"

2. **Transfers**:
   - "Transfer $100 from checking to savings"
   - "Move $50 from savings to checking"

3. **Recurring Payments**:
   - "Set up a monthly transfer of $200 from checking to savings starting next month"
   - "Show my recurring payments"
   - "Cancel my recurring payment"

4. **Transaction History**:
   - "Show my recent transactions"
   - "What transfers did I make this week?"

## 📝 Logs and Debugging

All logs are stored in the `logs/` directory:

- `backend.log` - Spring Boot application logs
- `mcp-server.log` - MCP server logs  
- `agents.log` - AI agents logs
- `frontend.log` - Streamlit frontend logs

## 🛑 Stopping Services

Press `Ctrl+C` in the terminal running `start-all.sh`, or:

```bash
# Kill all Java processes (backend)
pkill -f java

# Kill all Python processes (agents, frontend, MCP)
pkill -f python
```

## 🎉 Success Indicators

You'll know everything is working when:

1. ✅ All services start without errors
2. ✅ Frontend loads at http://localhost:8501
3. ✅ You can chat with the banking assistant
4. ✅ The assistant responds intelligently to your requests
5. ✅ Transfers and account operations work

## 💡 Tips

- The system uses **in-memory H2 database** - data resets on restart
- All AI processing happens via **OpenAI GPT-4**
- The **MCP protocol** enables tool-based AI interactions
- **A2A communication** allows agents to talk to each other
- **Natural language** understanding makes banking intuitive

---

**🎊 Enjoy your intelligent money transfer system!**

For detailed documentation, see the main [README.md](README.md) file.
