#!/usr/bin/env python3
"""
MCP Server for Money Transfer System
Provides tools for account management and money transfers
"""

import asyncio
import json
import logging
from typing import Any, Dict, List, Optional
from datetime import datetime, date

import httpx
from mcp.server import Server
from mcp.server.models import InitializationOptions
from mcp.server.stdio import stdio_server
from mcp.types import (
    Resource,
    Tool,
    TextContent,
    ImageContent,
    EmbeddedResource,
    LoggingLevel
)
from pydantic import BaseModel

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Backend API configuration
BACKEND_BASE_URL = "http://localhost:8080/api"

class TransferMCPServer:
    def __init__(self):
        self.server = Server("transfer-mcp-server")
        self.http_client = httpx.AsyncClient(timeout=30.0)
        self.setup_tools()
        
    def setup_tools(self):
        """Setup all MCP tools"""
        
        # Account Management Tools
        @self.server.list_tools()
        async def handle_list_tools() -> List[Tool]:
            """List available tools"""
            return [
                Tool(
                    name="create_account",
                    description="Create a new checking or savings account",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "accountName": {"type": "string", "description": "Name for the account"},
                            "accountType": {"type": "string", "enum": ["CHECKING", "SAVINGS"], "description": "Type of account"},
                            "initialDeposit": {"type": "number", "minimum": 0, "description": "Initial deposit amount"},
                            "customerId": {"type": "string", "description": "Customer ID"}
                        },
                        "required": ["accountName", "accountType", "initialDeposit", "customerId"]
                    }
                ),
                Tool(
                    name="get_accounts",
                    description="Get all accounts for a customer",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "customerId": {"type": "string", "description": "Customer ID"}
                        },
                        "required": ["customerId"]
                    }
                ),
                Tool(
                    name="get_account_balance",
                    description="Get balance for a specific account",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "accountId": {"type": "string", "description": "Account ID"}
                        },
                        "required": ["accountId"]
                    }
                ),
                Tool(
                    name="check_sufficient_funds",
                    description="Check if account has sufficient funds for a transfer",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "accountId": {"type": "string", "description": "Account ID"},
                            "amount": {"type": "number", "minimum": 0.01, "description": "Amount to check"}
                        },
                        "required": ["accountId", "amount"]
                    }
                ),
                Tool(
                    name="execute_immediate_transfer",
                    description="Execute an immediate transfer between accounts",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "fromAccountId": {"type": "string", "description": "Source account ID"},
                            "toAccountId": {"type": "string", "description": "Destination account ID"},
                            "amount": {"type": "number", "minimum": 0.01, "description": "Transfer amount"},
                            "customerId": {"type": "string", "description": "Customer ID"},
                            "description": {"type": "string", "description": "Transfer description"}
                        },
                        "required": ["fromAccountId", "toAccountId", "amount", "customerId"]
                    }
                ),
                Tool(
                    name="create_recurring_payment",
                    description="Create a recurring payment schedule",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "fromAccountId": {"type": "string", "description": "Source account ID"},
                            "toAccountId": {"type": "string", "description": "Destination account ID"},
                            "amount": {"type": "number", "minimum": 0.01, "description": "Payment amount"},
                            "frequency": {"type": "string", "enum": ["WEEKLY", "BI_WEEKLY", "MONTHLY"], "description": "Payment frequency"},
                            "startDate": {"type": "string", "format": "date", "description": "Start date (YYYY-MM-DD)"},
                            "customerId": {"type": "string", "description": "Customer ID"},
                            "description": {"type": "string", "description": "Payment description"}
                        },
                        "required": ["fromAccountId", "toAccountId", "amount", "frequency", "startDate", "customerId"]
                    }
                ),
                Tool(
                    name="get_transactions",
                    description="Get transaction history for a customer",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "customerId": {"type": "string", "description": "Customer ID"},
                            "transactionType": {"type": "string", "enum": ["IMMEDIATE", "RECURRING"], "description": "Filter by transaction type"},
                            "status": {"type": "string", "enum": ["PENDING", "COMPLETED", "FAILED", "CANCELLED"], "description": "Filter by status"}
                        },
                        "required": ["customerId"]
                    }
                ),
                Tool(
                    name="get_recurring_payments",
                    description="Get recurring payments for a customer",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "customerId": {"type": "string", "description": "Customer ID"},
                            "status": {"type": "string", "enum": ["ACTIVE", "PAUSED", "CANCELLED", "COMPLETED"], "description": "Filter by status"}
                        },
                        "required": ["customerId"]
                    }
                ),
                Tool(
                    name="manage_recurring_payment",
                    description="Pause, resume, or cancel a recurring payment",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "recurringPaymentId": {"type": "string", "description": "Recurring payment ID"},
                            "action": {"type": "string", "enum": ["pause", "resume", "cancel"], "description": "Action to perform"}
                        },
                        "required": ["recurringPaymentId", "action"]
                    }
                )
            ]

        @self.server.call_tool()
        async def handle_call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
            """Handle tool calls"""
            logger.info(f"Tool called: {name} with arguments: {arguments}")
            
            try:
                if name == "create_account":
                    return await self.create_account(arguments)
                elif name == "get_accounts":
                    return await self.get_accounts(arguments)
                elif name == "get_account_balance":
                    return await self.get_account_balance(arguments)
                elif name == "check_sufficient_funds":
                    return await self.check_sufficient_funds(arguments)
                elif name == "execute_immediate_transfer":
                    return await self.execute_immediate_transfer(arguments)
                elif name == "create_recurring_payment":
                    return await self.create_recurring_payment(arguments)
                elif name == "get_transactions":
                    return await self.get_transactions(arguments)
                elif name == "get_recurring_payments":
                    return await self.get_recurring_payments(arguments)
                elif name == "manage_recurring_payment":
                    return await self.manage_recurring_payment(arguments)
                else:
                    return [TextContent(type="text", text=f"Unknown tool: {name}")]
                    
            except Exception as e:
                logger.error(f"Error executing tool {name}: {str(e)}")
                return [TextContent(type="text", text=f"Error: {str(e)}")]

    async def create_account(self, args: Dict[str, Any]) -> List[TextContent]:
        """Create a new account"""
        logger.info(f"Creating account: {args}")
        
        try:
            response = await self.http_client.post(
                f"{BACKEND_BASE_URL}/accounts",
                json=args
            )
            response.raise_for_status()
            result = response.json()
            
            return [TextContent(
                type="text",
                text=f"Account created successfully!\n"
                     f"Account ID: {result['accountId']}\n"
                     f"Account Name: {result['accountName']}\n"
                     f"Account Type: {result['accountType']}\n"
                     f"Initial Balance: ${result['balance']}\n"
                     f"Created: {result['createdAt']}"
            )]
            
        except httpx.HTTPStatusError as e:
            error_msg = e.response.json().get('error', str(e))
            return [TextContent(type="text", text=f"Failed to create account: {error_msg}")]

    async def get_accounts(self, args: Dict[str, Any]) -> List[TextContent]:
        """Get all accounts for a customer"""
        logger.info(f"Getting accounts for customer: {args['customerId']}")
        
        try:
            response = await self.http_client.get(
                f"{BACKEND_BASE_URL}/accounts/customer/{args['customerId']}"
            )
            response.raise_for_status()
            accounts = response.json()
            
            if not accounts:
                return [TextContent(type="text", text="No accounts found for this customer.")]
            
            account_list = []
            for account in accounts:
                account_list.append(
                    f"• {account['accountName']} ({account['accountId']})\n"
                    f"  Type: {account['accountType']}\n"
                    f"  Balance: ${account['balance']}\n"
                    f"  Created: {account['createdAt']}"
                )
            
            return [TextContent(
                type="text",
                text=f"Accounts for customer {args['customerId']}:\n\n" + "\n\n".join(account_list)
            )]
            
        except httpx.HTTPStatusError as e:
            error_msg = e.response.json().get('error', str(e))
            return [TextContent(type="text", text=f"Failed to get accounts: {error_msg}")]

    async def get_account_balance(self, args: Dict[str, Any]) -> List[TextContent]:
        """Get account balance"""
        logger.info(f"Getting balance for account: {args['accountId']}")
        
        try:
            response = await self.http_client.get(
                f"{BACKEND_BASE_URL}/accounts/{args['accountId']}/balance"
            )
            response.raise_for_status()
            result = response.json()
            
            return [TextContent(
                type="text",
                text=f"Account Balance Information:\n"
                     f"Account: {result['accountName']} ({result['accountId']})\n"
                     f"Type: {result['accountType']}\n"
                     f"Balance: ${result['balance']}"
            )]
            
        except httpx.HTTPStatusError as e:
            if e.response.status_code == 404:
                return [TextContent(type="text", text=f"Account {args['accountId']} not found.")]
            error_msg = e.response.json().get('error', str(e))
            return [TextContent(type="text", text=f"Failed to get account balance: {error_msg}")]

    async def check_sufficient_funds(self, args: Dict[str, Any]) -> List[TextContent]:
        """Check if account has sufficient funds"""
        logger.info(f"Checking funds for account: {args['accountId']}, amount: {args['amount']}")
        
        try:
            response = await self.http_client.get(
                f"{BACKEND_BASE_URL}/accounts/{args['accountId']}/sufficient-funds/{args['amount']}"
            )
            response.raise_for_status()
            result = response.json()
            
            status = "✅ Sufficient" if result['hasSufficientFunds'] else "❌ Insufficient"
            
            return [TextContent(
                type="text",
                text=f"Funds Check Result:\n"
                     f"Account: {result['accountId']}\n"
                     f"Amount to check: ${result['amount']}\n"
                     f"Status: {status} funds"
            )]
            
        except httpx.HTTPStatusError as e:
            error_msg = e.response.json().get('error', str(e))
            return [TextContent(type="text", text=f"Failed to check funds: {error_msg}")]

    async def execute_immediate_transfer(self, args: Dict[str, Any]) -> List[TextContent]:
        """Execute immediate transfer"""
        logger.info(f"Executing immediate transfer: {args}")

        try:
            response = await self.http_client.post(
                f"{BACKEND_BASE_URL}/transactions/immediate",
                json=args
            )
            response.raise_for_status()
            result = response.json()

            return [TextContent(
                type="text",
                text=f"✅ Transfer completed successfully!\n"
                     f"Transaction ID: {result['transactionId']}\n"
                     f"From: {result['fromAccountId']}\n"
                     f"To: {result['toAccountId']}\n"
                     f"Amount: ${result['amount']}\n"
                     f"Status: {result['status']}\n"
                     f"Date: {result['createdAt']}\n"
                     f"Description: {result.get('description', 'N/A')}"
            )]

        except httpx.HTTPStatusError as e:
            error_msg = e.response.json().get('error', str(e))
            return [TextContent(type="text", text=f"❌ Transfer failed: {error_msg}")]

    async def create_recurring_payment(self, args: Dict[str, Any]) -> List[TextContent]:
        """Create recurring payment"""
        logger.info(f"Creating recurring payment: {args}")

        try:
            response = await self.http_client.post(
                f"{BACKEND_BASE_URL}/recurring-payments",
                json=args
            )
            response.raise_for_status()
            result = response.json()

            return [TextContent(
                type="text",
                text=f"✅ Recurring payment created successfully!\n"
                     f"Payment ID: {result['recurringPaymentId']}\n"
                     f"From: {result['fromAccountId']}\n"
                     f"To: {result['toAccountId']}\n"
                     f"Amount: ${result['amount']}\n"
                     f"Frequency: {result['frequency']}\n"
                     f"Start Date: {result['startDate']}\n"
                     f"Next Payment: {result['nextPaymentDate']}\n"
                     f"Status: {result['status']}\n"
                     f"Description: {result.get('description', 'N/A')}"
            )]

        except httpx.HTTPStatusError as e:
            error_msg = e.response.json().get('error', str(e))
            return [TextContent(type="text", text=f"❌ Failed to create recurring payment: {error_msg}")]

    async def get_transactions(self, args: Dict[str, Any]) -> List[TextContent]:
        """Get transaction history"""
        logger.info(f"Getting transactions: {args}")

        try:
            url = f"{BACKEND_BASE_URL}/transactions/customer/{args['customerId']}"

            # Add filters if provided
            if 'transactionType' in args:
                url = f"{BACKEND_BASE_URL}/transactions/customer/{args['customerId']}/type/{args['transactionType']}"
            elif 'status' in args:
                url = f"{BACKEND_BASE_URL}/transactions/customer/{args['customerId']}/status/{args['status']}"

            response = await self.http_client.get(url)
            response.raise_for_status()
            transactions = response.json()

            if not transactions:
                return [TextContent(type="text", text="No transactions found.")]

            # Separate immediate and recurring transactions
            immediate_txns = [t for t in transactions if t['transactionType'] == 'IMMEDIATE']
            recurring_txns = [t for t in transactions if t['transactionType'] == 'RECURRING']

            result_text = f"Transaction History for Customer {args['customerId']}:\n\n"

            if immediate_txns:
                result_text += "🔄 IMMEDIATE TRANSFERS:\n"
                for txn in immediate_txns[:10]:  # Show latest 10
                    status_icon = "✅" if txn['status'] == 'COMPLETED' else "❌" if txn['status'] == 'FAILED' else "⏳"
                    result_text += (
                        f"{status_icon} {txn['transactionId']}\n"
                        f"   From: {txn['fromAccountId']} → To: {txn['toAccountId']}\n"
                        f"   Amount: ${txn['amount']} | Status: {txn['status']}\n"
                        f"   Date: {txn['createdAt']}\n\n"
                    )

            if recurring_txns:
                result_text += "🔁 RECURRING TRANSFERS:\n"
                for txn in recurring_txns[:10]:  # Show latest 10
                    status_icon = "✅" if txn['status'] == 'COMPLETED' else "❌" if txn['status'] == 'FAILED' else "⏳"
                    result_text += (
                        f"{status_icon} {txn['transactionId']}\n"
                        f"   From: {txn['fromAccountId']} → To: {txn['toAccountId']}\n"
                        f"   Amount: ${txn['amount']} | Status: {txn['status']}\n"
                        f"   Date: {txn['createdAt']}\n"
                        f"   Recurring ID: {txn.get('recurringPaymentId', 'N/A')}\n\n"
                    )

            return [TextContent(type="text", text=result_text)]

        except httpx.HTTPStatusError as e:
            error_msg = e.response.json().get('error', str(e))
            return [TextContent(type="text", text=f"Failed to get transactions: {error_msg}")]

    async def get_recurring_payments(self, args: Dict[str, Any]) -> List[TextContent]:
        """Get recurring payments"""
        logger.info(f"Getting recurring payments: {args}")

        try:
            url = f"{BACKEND_BASE_URL}/recurring-payments/customer/{args['customerId']}"

            # Add status filter if provided
            if 'status' in args:
                url = f"{BACKEND_BASE_URL}/recurring-payments/customer/{args['customerId']}/status/{args['status']}"

            response = await self.http_client.get(url)
            response.raise_for_status()
            payments = response.json()

            if not payments:
                return [TextContent(type="text", text="No recurring payments found.")]

            result_text = f"Recurring Payments for Customer {args['customerId']}:\n\n"

            for payment in payments:
                status_icon = {
                    'ACTIVE': '🟢',
                    'PAUSED': '⏸️',
                    'CANCELLED': '❌',
                    'COMPLETED': '✅'
                }.get(payment['status'], '❓')

                result_text += (
                    f"{status_icon} {payment['recurringPaymentId']}\n"
                    f"   From: {payment['fromAccountId']} → To: {payment['toAccountId']}\n"
                    f"   Amount: ${payment['amount']} | Frequency: {payment['frequency']}\n"
                    f"   Status: {payment['status']}\n"
                    f"   Next Payment: {payment['nextPaymentDate']}\n"
                    f"   Created: {payment['createdAt']}\n"
                    f"   Description: {payment.get('description', 'N/A')}\n\n"
                )

            return [TextContent(type="text", text=result_text)]

        except httpx.HTTPStatusError as e:
            error_msg = e.response.json().get('error', str(e))
            return [TextContent(type="text", text=f"Failed to get recurring payments: {error_msg}")]

    async def manage_recurring_payment(self, args: Dict[str, Any]) -> List[TextContent]:
        """Manage recurring payment (pause/resume/cancel)"""
        logger.info(f"Managing recurring payment: {args}")

        try:
            action = args['action']
            payment_id = args['recurringPaymentId']

            response = await self.http_client.put(
                f"{BACKEND_BASE_URL}/recurring-payments/{payment_id}/{action}"
            )
            response.raise_for_status()
            result = response.json()

            action_past = {
                'pause': 'paused',
                'resume': 'resumed',
                'cancel': 'cancelled'
            }.get(action, action)

            return [TextContent(
                type="text",
                text=f"✅ Recurring payment {action_past} successfully!\n"
                     f"Payment ID: {result['recurringPaymentId']}\n"
                     f"New Status: {result['status']}\n"
                     f"From: {result['fromAccountId']} → To: {result['toAccountId']}\n"
                     f"Amount: ${result['amount']}\n"
                     f"Frequency: {result['frequency']}"
            )]

        except httpx.HTTPStatusError as e:
            error_msg = e.response.json().get('error', str(e))
            return [TextContent(type="text", text=f"❌ Failed to {args['action']} recurring payment: {error_msg}")]

    async def run(self):
        """Run the MCP server"""
        logger.info("Starting Transfer MCP Server...")

        async with stdio_server() as (read_stream, write_stream):
            await self.server.run(
                read_stream,
                write_stream,
                InitializationOptions(
                    server_name="transfer-mcp-server",
                    server_version="1.0.0",
                    capabilities=self.server.get_capabilities(
                        notification_options=None,
                        experimental_capabilities=None,
                    ),
                ),
            )

async def main():
    """Main entry point"""
    server = TransferMCPServer()
    await server.run()

if __name__ == "__main__":
    asyncio.run(main())
