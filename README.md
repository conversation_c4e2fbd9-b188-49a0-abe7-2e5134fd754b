# 💰 Intelligent Money Transfer System

A comprehensive intelligent banking system that allows customers to manage accounts and perform money transfers using natural language processing. The system features a Streamlit frontend, Spring Boot backend, MCP servers, Google ADK agents, and OpenAI integration.

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Streamlit     │    │  Client Agent   │    │  Server Agent   │
│   Frontend      │◄──►│  (Google ADK)   │◄──►│  (Google ADK)   │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                        │
                                │                        ▼
                                │               ┌─────────────────┐
                                │               │   MCP Server    │
                                │               │                 │
                                │               └─────────────────┘
                                │                        │
                                │                        ▼
                                │               ┌─────────────────┐
                                └──────────────►│  Spring Boot    │
                                                │   Backend API   │
                                                │                 │
                                                └─────────────────┘
```

## 🚀 Features

### Core Banking Features
- ✅ **Account Management**: Create checking and savings accounts with initial deposits
- ✅ **Immediate Transfers**: Real-time money transfers between accounts
- ✅ **Recurring Payments**: Schedule weekly, bi-weekly, or monthly transfers
- ✅ **Transaction History**: View complete transaction history with filtering
- ✅ **Balance Checking**: Real-time account balance inquiries
- ✅ **Fund Validation**: Automatic insufficient funds checking

### Intelligent Features
- 🤖 **Natural Language Processing**: Communicate in plain English
- 🧠 **Intent Recognition**: AI understands customer requests automatically
- 💬 **Conversational Interface**: Context-aware chat experience
- 🔍 **Smart Suggestions**: AI provides helpful recommendations
- 📊 **Transaction Analytics**: Visual dashboards and insights

### Technical Features
- 🏦 **Spring Boot Backend**: Robust REST API with validation
- 🔧 **MCP Integration**: Model Context Protocol for tool execution
- 🌐 **Google ADK Agents**: Advanced AI agent communication
- 🎨 **Streamlit Frontend**: Modern, responsive web interface
- 📝 **Comprehensive Logging**: Full system observability

## 📋 Prerequisites

- **Java 17+** (for Spring Boot backend)
- **Python 3.8+** (for MCP server, agents, and frontend)
- **Node.js 16+** (optional, for additional tooling)
- **OpenAI API Key** (for LLM functionality)
- **Google Cloud Account** (optional, for Google ADK)

## 🛠️ Installation & Setup

### 1. Clone the Repository
```bash
git clone <repository-url>
cd TransferProject
```

### 2. Environment Configuration
```bash
# Copy environment template
cp config/.env.example config/.env

# Edit the configuration file
nano config/.env
```

Add your API keys:
```env
OPENAI_API_KEY=your_openai_api_key_here
GOOGLE_CLOUD_PROJECT_ID=your_project_id
GOOGLE_CLOUD_LOCATION=us-central1
```

### 3. Make Scripts Executable
```bash
chmod +x scripts/*.sh
```

### 4. Start All Services
```bash
# Start everything with one command
./scripts/start-all.sh
```

Or start services individually:

```bash
# Terminal 1: Start Backend
./scripts/start-backend.sh

# Terminal 2: Start MCP Server
./scripts/start-mcp-server.sh

# Terminal 3: Start AI Agents
./scripts/start-agents.sh

# Terminal 4: Start Frontend
./scripts/start-frontend.sh
```

## 🌐 Access Points

Once all services are running:

- **Frontend Application**: http://localhost:8501
- **Backend API**: http://localhost:8080
- **API Documentation**: http://localhost:8080/swagger-ui.html
- **H2 Database Console**: http://localhost:8080/h2-console
- **Health Check**: http://localhost:8080/actuator/health

## 💬 Usage Examples

### Natural Language Commands

The system understands natural language requests:

```
"Show me my account balances"
"Transfer $100 from checking to savings"
"Set up a monthly transfer of $200 from checking to savings starting next month"
"What are my recent transactions?"
"Cancel my recurring payment REC-PAY001"
```

### API Examples

Direct API usage:

```bash
# Create an account
curl -X POST http://localhost:8080/api/accounts \
  -H "Content-Type: application/json" \
  -d '{
    "accountName": "My Checking",
    "accountType": "CHECKING",
    "initialDeposit": 1000.00,
    "customerId": "CUST001"
  }'

# Execute transfer
curl -X POST http://localhost:8080/api/transactions/immediate \
  -H "Content-Type: application/json" \
  -d '{
    "fromAccountId": "CHK-********",
    "toAccountId": "SAV-********",
    "amount": 100.00,
    "customerId": "CUST001",
    "description": "Monthly savings"
  }'
```

## 🏗️ Component Details

### Spring Boot Backend (`src/main/java/com/transfer/`)
- **Models**: Account, Transaction, RecurringPayment entities
- **Services**: Business logic with validation and processing
- **Controllers**: REST API endpoints
- **Repositories**: JPA data access layer

### MCP Server (`mcp-server/`)
- **Tools**: Account management, transfers, transaction history
- **Protocol**: MCP 1.0 compliant server
- **Integration**: Direct API communication with backend

### AI Agents (`agents/`)
- **Server Agent**: Processes requests using MCP tools
- **Client Agent**: Handles user interactions and A2A communication
- **LLM Integration**: OpenAI GPT-4 for natural language understanding

### Frontend (`frontend/`)
- **Chat Interface**: Natural language banking assistant
- **Dashboard**: Transaction analytics and visualizations
- **Account Management**: Real-time account information

## 📊 System Monitoring

### Logs Location
- Backend: `logs/backend.log`
- MCP Server: `logs/mcp-server.log`
- AI Agents: `logs/agents.log`
- Frontend: `logs/frontend.log`

### Health Checks
```bash
# Backend health
curl http://localhost:8080/actuator/health

# Check all services
./scripts/check-health.sh
```

## 🔧 Development

### Running Tests
```bash
# Backend tests
./gradlew test

# Python tests
cd mcp-server && python -m pytest
cd agents && python -m pytest
```

### Database Access
- **URL**: `jdbc:h2:mem:transferdb`
- **Username**: `sa`
- **Password**: `password`

### API Documentation
Visit http://localhost:8080/swagger-ui.html for interactive API documentation.

## 🐳 Docker Deployment

```bash
# Build and start with Docker Compose
docker-compose up --build

# Stop services
docker-compose down
```

## 🔍 Troubleshooting

### Common Issues

1. **Port Conflicts**
   ```bash
   # Check if ports are in use
   lsof -i :8080  # Backend
   lsof -i :8501  # Frontend
   ```

2. **Java Version Issues**
   ```bash
   # Check Java version
   java -version
   # Should be 17 or higher
   ```

3. **Python Dependencies**
   ```bash
   # Recreate virtual environment
   rm -rf venv
   python3 -m venv venv
   source venv/bin/activate
   pip install -r requirements.txt
   ```

4. **OpenAI API Issues**
   - Verify API key in `config/.env`
   - Check API quota and billing

### Log Analysis
```bash
# View real-time logs
tail -f logs/backend.log
tail -f logs/mcp-server.log
tail -f logs/agents.log
tail -f logs/frontend.log
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Check the troubleshooting section
- Review the logs in the `logs/` directory
- Open an issue on GitHub

---

**Built with ❤️ using Spring Boot, MCP, Google ADK, OpenAI, and Streamlit**
