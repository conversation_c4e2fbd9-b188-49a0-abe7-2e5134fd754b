#!/usr/bin/env python3
"""
Streamlit Frontend for Intelligent Money Transfer System
"""

import streamlit as st
import asyncio
import sys
import os
import json
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, date
from typing import Dict, List, Any

# Add the agents directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'agents'))

from client_agent import ClientAgent

# Configure Streamlit page
st.set_page_config(
    page_title="Intelligent Money Transfer System",
    page_icon="💰",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .chat-message {
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 0.5rem 0;
    }
    .user-message {
        background-color: #e3f2fd;
        border-left: 4px solid #2196f3;
    }
    .assistant-message {
        background-color: #f3e5f5;
        border-left: 4px solid #9c27b0;
    }
    .account-card {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 0.5rem;
        border: 1px solid #dee2e6;
        margin: 0.5rem 0;
    }
    .transaction-item {
        background-color: #ffffff;
        padding: 0.8rem;
        border-radius: 0.3rem;
        border: 1px solid #e9ecef;
        margin: 0.3rem 0;
    }
</style>
""", unsafe_allow_html=True)

class StreamlitApp:
    def __init__(self):
        self.client_agent = None
        self.initialize_session_state()
        
    def initialize_session_state(self):
        """Initialize Streamlit session state"""
        if 'customer_id' not in st.session_state:
            st.session_state.customer_id = "CUST001"  # Default customer
        if 'conversation_history' not in st.session_state:
            st.session_state.conversation_history = []
        if 'client_agent' not in st.session_state:
            st.session_state.client_agent = ClientAgent()
        if 'accounts_data' not in st.session_state:
            st.session_state.accounts_data = []
        if 'transactions_data' not in st.session_state:
            st.session_state.transactions_data = []
        if 'recurring_payments_data' not in st.session_state:
            st.session_state.recurring_payments_data = []

    async def initialize_conversation(self):
        """Initialize conversation with the client agent"""
        if not st.session_state.conversation_history:
            greeting = await st.session_state.client_agent.start_conversation(st.session_state.customer_id)
            st.session_state.conversation_history.append({
                "role": "assistant",
                "content": greeting,
                "timestamp": datetime.now().isoformat()
            })

    def render_header(self):
        """Render the main header"""
        st.markdown('<h1 class="main-header">💰 Intelligent Money Transfer System</h1>', unsafe_allow_html=True)
        
        # Customer selection
        col1, col2, col3 = st.columns([1, 2, 1])
        with col2:
            customer_id = st.selectbox(
                "Select Customer ID:",
                ["CUST001", "CUST002", "CUST003"],
                index=0
            )
            if customer_id != st.session_state.customer_id:
                st.session_state.customer_id = customer_id
                st.session_state.conversation_history = []
                st.rerun()

    def render_sidebar(self):
        """Render the sidebar with account information and quick actions"""
        st.sidebar.header("🏦 Account Dashboard")
        
        # Mock account data for display
        accounts = [
            {"name": "My Checking", "id": "CHK-********", "type": "CHECKING", "balance": 1250.00},
            {"name": "My Savings", "id": "SAV-********", "type": "SAVINGS", "balance": 5000.00}
        ]
        
        st.sidebar.subheader("Your Accounts")
        for account in accounts:
            with st.sidebar.container():
                st.markdown(f"""
                <div class="account-card">
                    <strong>{account['name']}</strong><br>
                    <small>{account['id']}</small><br>
                    <span style="font-size: 1.2em; color: #28a745;">${account['balance']:,.2f}</span>
                </div>
                """, unsafe_allow_html=True)
        
        st.sidebar.divider()
        
        # Quick actions
        st.sidebar.subheader("Quick Actions")
        if st.sidebar.button("💰 Check Balances", use_container_width=True):
            self.add_user_message("Show me all my account balances")
        
        if st.sidebar.button("💸 Transfer Money", use_container_width=True):
            self.add_user_message("I want to transfer money between my accounts")
        
        if st.sidebar.button("📅 Recurring Payments", use_container_width=True):
            self.add_user_message("Show me my recurring payments")
        
        if st.sidebar.button("📊 Transaction History", use_container_width=True):
            self.add_user_message("Show me my recent transactions")
        
        st.sidebar.divider()
        
        # System status
        st.sidebar.subheader("System Status")
        st.sidebar.success("🟢 Backend API: Online")
        st.sidebar.success("🟢 MCP Server: Online")
        st.sidebar.success("🟢 AI Agents: Ready")

    def add_user_message(self, message: str):
        """Add user message and trigger processing"""
        st.session_state.conversation_history.append({
            "role": "user",
            "content": message,
            "timestamp": datetime.now().isoformat()
        })
        st.rerun()

    def render_chat_interface(self):
        """Render the main chat interface"""
        st.header("💬 Natural Language Banking Assistant")
        
        # Chat container
        chat_container = st.container()
        
        with chat_container:
            # Display conversation history
            for message in st.session_state.conversation_history:
                if message["role"] == "user":
                    st.markdown(f"""
                    <div class="chat-message user-message">
                        <strong>You:</strong> {message["content"]}
                    </div>
                    """, unsafe_allow_html=True)
                else:
                    st.markdown(f"""
                    <div class="chat-message assistant-message">
                        <strong>Assistant:</strong> {message["content"]}
                    </div>
                    """, unsafe_allow_html=True)
        
        # Chat input
        user_input = st.chat_input("Type your banking request here... (e.g., 'Transfer $100 from checking to savings')")
        
        if user_input:
            # Add user message
            st.session_state.conversation_history.append({
                "role": "user",
                "content": user_input,
                "timestamp": datetime.now().isoformat()
            })
            
            # Process with client agent (simulated for now)
            with st.spinner("Processing your request..."):
                # Simulate AI processing
                response = self.simulate_ai_response(user_input)
                
                st.session_state.conversation_history.append({
                    "role": "assistant",
                    "content": response,
                    "timestamp": datetime.now().isoformat()
                })
            
            st.rerun()

    def simulate_ai_response(self, user_input: str) -> str:
        """Simulate AI response for demo purposes"""
        user_input_lower = user_input.lower()
        
        if "balance" in user_input_lower or "account" in user_input_lower:
            return """Here are your current account balances:

• **My Checking** (CHK-********): $1,250.00
• **My Savings** (SAV-********): $5,000.00

Would you like to make a transfer or check something else?"""
        
        elif "transfer" in user_input_lower:
            if "100" in user_input and "checking" in user_input_lower and "savings" in user_input_lower:
                return """✅ **Transfer completed successfully!**

**Transaction Details:**
- Transaction ID: TXN-ABC********9
- From: My Checking (CHK-********)
- To: My Savings (SAV-********)
- Amount: $100.00
- Status: COMPLETED
- Date: """ + datetime.now().strftime("%Y-%m-%d %H:%M:%S") + """

Your new balances:
- My Checking: $1,150.00
- My Savings: $5,100.00

Is there anything else I can help you with?"""
            else:
                return """I'd be happy to help you with a transfer! 

To process your transfer, I need:
1. **From account** - Which account to transfer from?
2. **To account** - Which account to transfer to?
3. **Amount** - How much would you like to transfer?

Your available accounts:
• My Checking (CHK-********) - $1,250.00
• My Savings (SAV-********) - $5,000.00

Please let me know the details!"""
        
        elif "recurring" in user_input_lower or "schedule" in user_input_lower:
            return """Here are your recurring payments:

🟢 **Active Recurring Payments:**
• REC-PAY001: $200.00 monthly from Checking to Savings
  - Next payment: 2024-01-15
  - Status: Active

Would you like to:
- Create a new recurring payment
- Modify an existing payment
- Cancel a payment

Just let me know what you'd like to do!"""
        
        elif "transaction" in user_input_lower or "history" in user_input_lower:
            return """Here's your recent transaction history:

**🔄 IMMEDIATE TRANSFERS:**
✅ TXN-ABC********9 - $100.00 (Checking → Savings) - Today 2:30 PM
✅ TXN-DEF9******** - $50.00 (Savings → Checking) - Yesterday 10:15 AM
✅ TXN-GHI456789123 - $200.00 (Checking → Savings) - Dec 28, 2024

**🔁 RECURRING TRANSFERS:**
✅ TXN-REC001 - $200.00 (Checking → Savings) - Dec 15, 2024
✅ TXN-REC002 - $200.00 (Checking → Savings) - Nov 15, 2024

Would you like to see more details about any specific transaction?"""
        
        else:
            return """I'm here to help with your banking needs! I can assist you with:

• **Account Management** - Check balances, view account details
• **Money Transfers** - Immediate transfers between your accounts
• **Recurring Payments** - Set up, modify, or cancel scheduled transfers
• **Transaction History** - View your recent banking activity
• **Account Creation** - Open new checking or savings accounts

What would you like to do today?"""

    def render_transaction_dashboard(self):
        """Render transaction dashboard with charts"""
        st.header("📊 Transaction Dashboard")
        
        # Mock transaction data for visualization
        transaction_data = [
            {"date": "2024-01-01", "type": "Transfer", "amount": 100, "status": "Completed"},
            {"date": "2024-01-02", "type": "Transfer", "amount": 50, "status": "Completed"},
            {"date": "2024-01-03", "type": "Recurring", "amount": 200, "status": "Completed"},
            {"date": "2024-01-04", "type": "Transfer", "amount": 75, "status": "Completed"},
            {"date": "2024-01-05", "type": "Transfer", "amount": 25, "status": "Failed"},
        ]
        
        df = pd.DataFrame(transaction_data)
        df['date'] = pd.to_datetime(df['date'])
        
        col1, col2 = st.columns(2)
        
        with col1:
            # Transaction volume chart
            fig_volume = px.bar(
                df, 
                x='date', 
                y='amount', 
                color='type',
                title="Daily Transaction Volume",
                labels={'amount': 'Amount ($)', 'date': 'Date'}
            )
            st.plotly_chart(fig_volume, use_container_width=True)
        
        with col2:
            # Transaction status pie chart
            status_counts = df['status'].value_counts()
            fig_status = px.pie(
                values=status_counts.values,
                names=status_counts.index,
                title="Transaction Status Distribution"
            )
            st.plotly_chart(fig_status, use_container_width=True)

    def render_logs_section(self):
        """Render system logs for debugging"""
        with st.expander("🔍 System Logs & Debug Information"):
            st.subheader("Component Status")
            
            col1, col2, col3 = st.columns(3)
            
            with col1:
                st.success("**Spring Boot Backend**")
                st.text("Status: ✅ Running")
                st.text("Port: 8080")
                st.text("Database: H2 In-Memory")
            
            with col2:
                st.success("**MCP Server**")
                st.text("Status: ✅ Running")
                st.text("Tools: 9 Available")
                st.text("Protocol: MCP 1.0")
            
            with col3:
                st.success("**AI Agents**")
                st.text("Status: ✅ Ready")
                st.text("LLM: OpenAI GPT-4")
                st.text("Protocol: A2A")
            
            st.subheader("Recent System Activity")
            logs = [
                f"{datetime.now().strftime('%H:%M:%S')} - Client Agent: Processing user request",
                f"{datetime.now().strftime('%H:%M:%S')} - Server Agent: Analyzing intent with LLM",
                f"{datetime.now().strftime('%H:%M:%S')} - MCP Server: Executing transfer tool",
                f"{datetime.now().strftime('%H:%M:%S')} - Backend API: Transaction completed successfully",
                f"{datetime.now().strftime('%H:%M:%S')} - Client Agent: Generating response"
            ]
            
            for log in logs:
                st.text(log)

    def run(self):
        """Main application runner"""
        # Initialize conversation
        asyncio.run(self.initialize_conversation())
        
        # Render components
        self.render_header()
        self.render_sidebar()
        
        # Main content tabs
        tab1, tab2, tab3 = st.tabs(["💬 Chat Assistant", "📊 Dashboard", "🔍 System Logs"])
        
        with tab1:
            self.render_chat_interface()
        
        with tab2:
            self.render_transaction_dashboard()
        
        with tab3:
            self.render_logs_section()

def main():
    """Main entry point"""
    app = StreamlitApp()
    app.run()

if __name__ == "__main__":
    main()
