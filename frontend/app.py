#!/usr/bin/env python3
"""
Streamlit Frontend for Intelligent Money Transfer System
"""

import streamlit as st
import asyncio
import sys
import os
import json
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
import requests
from datetime import datetime, date
from typing import Dict, List, Any

# Configure Streamlit page
st.set_page_config(
    page_title="Intelligent Money Transfer System",
    page_icon="💰",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .chat-message {
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 0.5rem 0;
        color: #000000 !important;
        font-size: 14px;
        line-height: 1.5;
    }
    .user-message {
        background-color: #e8f4fd !important;
        border-left: 4px solid #4CAF50;
        color: #000000 !important;
        border: 1px solid #b3d9ff;
    }
    .assistant-message {
        background-color: #ffffff !important;
        border-left: 4px solid #2196F3;
        color: #000000 !important;
        border: 1px solid #e0e0e0;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }
    .chat-message strong {
        color: #000000 !important;
        font-weight: bold;
    }
    .chat-message p {
        color: #000000 !important;
        margin: 0;
    }
    .account-card {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 0.5rem;
        border: 1px solid #dee2e6;
        margin: 0.5rem 0;
    }
    .transaction-item {
        background-color: #ffffff;
        padding: 0.8rem;
        border-radius: 0.3rem;
        border: 1px solid #e9ecef;
        margin: 0.3rem 0;
    }
    /* Override Streamlit's default dark theme for chat messages */
    .stApp [data-testid="stChatMessage"] {
        background-color: transparent !important;
    }
    .stApp [data-testid="stChatMessage"] > div {
        background-color: transparent !important;
        color: #000000 !important;
    }
</style>
""", unsafe_allow_html=True)

class StreamlitApp:
    def __init__(self):
        self.client_agent_url = "http://localhost:8766"
        self.initialize_session_state()

    def initialize_session_state(self):
        """Initialize Streamlit session state"""
        if 'customer_id' not in st.session_state:
            st.session_state.customer_id = "DEFAULT_USER"  # No login required
        if 'conversation_history' not in st.session_state:
            st.session_state.conversation_history = []
        if 'conversation_initialized' not in st.session_state:
            st.session_state.conversation_initialized = False

    def initialize_conversation(self):
        """Initialize conversation with the client agent"""
        if not st.session_state.conversation_initialized:
            try:
                response = requests.post(
                    f"{self.client_agent_url}/start_conversation",
                    json={"customer_id": st.session_state.customer_id},
                    timeout=10
                )

                if response.status_code == 200:
                    data = response.json()
                    greeting = data.get("greeting", "Hello! I'm your banking assistant.")

                    st.session_state.conversation_history.append({
                        "role": "assistant",
                        "content": greeting,
                        "timestamp": datetime.now().isoformat()
                    })
                    st.session_state.conversation_initialized = True
                else:
                    st.error("Failed to connect to banking assistant. Please check if all services are running.")

            except requests.exceptions.RequestException as e:
                st.warning(f"Cannot connect to AI Agent. Using demo mode. To enable full functionality, please start the agents with: ./scripts/start-agents.sh")
                # Add a fallback greeting
                st.session_state.conversation_history.append({
                    "role": "assistant",
                    "content": """Hello! I'm your banking assistant.

**Demo Mode Active** - To enable full AI functionality:
1. Set your OpenAI API key: `export OPENAI_API_KEY=your-key`
2. Start the agents: `./scripts/start-agents.sh`

I can still help you explore the interface and see sample responses!""",
                    "timestamp": datetime.now().isoformat()
                })
                st.session_state.conversation_initialized = True

    def render_header(self):
        """Render the main header"""
        st.markdown('<h1 class="main-header">💰 Intelligent Money Transfer System</h1>', unsafe_allow_html=True)

        # Welcome message without login
        col1, col2, col3 = st.columns([1, 2, 1])
        with col2:
            st.markdown("""
            <div style="text-align: center; padding: 1rem; background-color: #f0f8ff; border-radius: 0.5rem; margin-bottom: 1rem;">
                <h3 style="color: #1f77b4; margin: 0;">Welcome to Your Banking Assistant!</h3>
                <p style="margin: 0.5rem 0 0 0; color: #666;">Start chatting to manage your accounts and transfers</p>
            </div>
            """, unsafe_allow_html=True)

    def render_sidebar(self):
        """Render the sidebar with account information and quick actions"""
        st.sidebar.header("🏦 Your Banking Dashboard")

        # Mock account data for display
        accounts = [
            {"name": "My Checking", "id": "CHK-********", "type": "CHECKING", "balance": 1250.00},
            {"name": "My Savings", "id": "SAV-********", "type": "SAVINGS", "balance": 5000.00}
        ]

        st.sidebar.subheader("Your Accounts")
        for account in accounts:
            with st.sidebar.container():
                st.markdown(f"""
                <div class="account-card">
                    <strong>{account['name']}</strong><br>
                    <small>{account['id']}</small><br>
                    <span style="font-size: 1.2em; color: #28a745;">${account['balance']:,.2f}</span>
                </div>
                """, unsafe_allow_html=True)

        st.sidebar.divider()

        # Quick actions
        st.sidebar.subheader("Quick Actions")
        if st.sidebar.button("💰 Check Balances", use_container_width=True):
            self.add_user_message("Show me all my account balances")

        if st.sidebar.button("💸 Transfer Money", use_container_width=True):
            self.add_user_message("I want to transfer money between my accounts")

        if st.sidebar.button("📅 Recurring Payments", use_container_width=True):
            self.add_user_message("Show me my recurring payments")

        if st.sidebar.button("📊 Transaction History", use_container_width=True):
            self.add_user_message("Show me my recent transactions")

        if st.sidebar.button("🏦 Create Account", use_container_width=True):
            self.add_user_message("I want to create a new account")

        st.sidebar.divider()

        # System status
        st.sidebar.subheader("System Status")
        st.sidebar.success("🟢 Backend API: Online")
        st.sidebar.success("🟢 MCP Server: Online")
        st.sidebar.success("🟢 AI Agents: Ready")

    def add_user_message(self, message: str):
        """Add user message and trigger processing"""
        st.session_state.conversation_history.append({
            "role": "user",
            "content": message,
            "timestamp": datetime.now().isoformat()
        })
        st.rerun()

    def render_chat_interface(self):
        """Render the main chat interface"""
        st.header("💬 Chat with Your Banking Assistant")

        # Instructions
        st.markdown("""
        <div style="background-color: #f0f8ff; padding: 1rem; border-radius: 0.5rem; margin-bottom: 1rem; border-left: 4px solid #2196F3;">
            <strong>💡 Try these commands:</strong><br>
            • "Show me my account balances"<br>
            • "Transfer $100 from checking to savings"<br>
            • "Set up a monthly transfer of $200"<br>
            • "What are my recent transactions?"<br>
            • "Create a new savings account with $500"
        </div>
        """, unsafe_allow_html=True)

        # Chat container
        chat_container = st.container()

        with chat_container:
            # Display conversation history using Streamlit's built-in chat components
            for message in st.session_state.conversation_history:
                if message["role"] == "user":
                    with st.chat_message("user"):
                        st.write(message["content"])
                else:
                    with st.chat_message("assistant"):
                        st.write(message["content"])

        # Chat input
        user_input = st.chat_input("Type your banking request here in natural language...")

        if user_input:
            # Add user message
            st.session_state.conversation_history.append({
                "role": "user",
                "content": user_input,
                "timestamp": datetime.now().isoformat()
            })

            # Process with client agent
            with st.spinner("🤖 Processing your request..."):
                response = self.process_with_client_agent(user_input)

                st.session_state.conversation_history.append({
                    "role": "assistant",
                    "content": response,
                    "timestamp": datetime.now().isoformat()
                })

            st.rerun()

    def process_with_client_agent(self, user_input: str) -> str:
        """Process user input with the Client Agent"""
        try:
            response = requests.post(
                f"{self.client_agent_url}/chat",
                json={
                    "customer_id": st.session_state.customer_id,
                    "message": user_input
                },
                timeout=30
            )

            if response.status_code == 200:
                data = response.json()
                return data.get("response", "I apologize, but I couldn't process your request.")
            else:
                return f"Error: {response.status_code} - {response.text}"

        except requests.exceptions.RequestException as e:
            # Fallback to demo responses when agents are not available
            return self.get_demo_response(user_input)

    def get_demo_response(self, user_input: str) -> str:
        """Provide demo responses when AI agents are not available"""
        user_input_lower = user_input.lower()

        if "balance" in user_input_lower or "account" in user_input_lower:
            return """**Demo Response** - Here are your current account balances:

• **My Checking** (CHK-********): $1,250.00
• **My Savings** (SAV-********): $5,000.00

*Note: This is a demo response. For real AI-powered responses, please start the agents with your OpenAI API key.*

Would you like to make a transfer or check something else?"""

        elif "transfer" in user_input_lower:
            if "100" in user_input and "checking" in user_input_lower and "savings" in user_input_lower:
                return """**Demo Response** - ✅ Transfer completed successfully!

**Transaction Details:**
- Transaction ID: TXN-DEMO********9
- From: My Checking (CHK-********)
- To: My Savings (SAV-********)
- Amount: $100.00
- Status: COMPLETED
- Date: """ + datetime.now().strftime("%Y-%m-%d %H:%M:%S") + """

Your new balances:
- My Checking: $1,150.00
- My Savings: $5,100.00

*Note: This is a demo response. For real transactions, please start the full system.*

Is there anything else I can help you with?"""
            else:
                return """**Demo Response** - I'd be happy to help you with a transfer!

To process your transfer, I need:
1. **From account** - Which account to transfer from?
2. **To account** - Which account to transfer to?
3. **Amount** - How much would you like to transfer?

Your available accounts:
• My Checking (CHK-********) - $1,250.00
• My Savings (SAV-********) - $5,000.00

*Note: This is a demo response. For real AI-powered assistance, please start the agents.*

Please let me know the details!"""

        elif "recurring" in user_input_lower or "schedule" in user_input_lower:
            return """**Demo Response** - Here are your recurring payments:

🟢 **Active Recurring Payments:**
• REC-PAY001: $200.00 monthly from Checking to Savings
  - Next payment: 2024-01-15
  - Status: Active

Would you like to:
- Create a new recurring payment
- Modify an existing payment
- Cancel a payment

*Note: This is a demo response. For real functionality, please start the full system.*

Just let me know what you'd like to do!"""

        elif "transaction" in user_input_lower or "history" in user_input_lower:
            return """**Demo Response** - Here's your recent transaction history:

**🔄 IMMEDIATE TRANSFERS:**
✅ TXN-ABC********9 - $100.00 (Checking → Savings) - Today 2:30 PM
✅ TXN-DEF9******** - $50.00 (Savings → Checking) - Yesterday 10:15 AM
✅ TXN-GHI456789123 - $200.00 (Checking → Savings) - Dec 28, 2024

**🔁 RECURRING TRANSFERS:**
✅ TXN-REC001 - $200.00 (Checking → Savings) - Dec 15, 2024
✅ TXN-REC002 - $200.00 (Checking → Savings) - Nov 15, 2024

*Note: This is a demo response. For real transaction data, please start the full system.*

Would you like to see more details about any specific transaction?"""

        else:
            return """**Demo Mode** - I'm here to help with your banking needs! I can assist you with:

• **Account Management** - Check balances, view account details
• **Money Transfers** - Immediate transfers between your accounts
• **Recurring Payments** - Set up, modify, or cancel scheduled transfers
• **Transaction History** - View your recent banking activity
• **Account Creation** - Open new checking or savings accounts

*Note: Currently running in demo mode. For full AI-powered functionality:*
1. Set your OpenAI API key: `export OPENAI_API_KEY=your-key`
2. Start the agents: `./scripts/start-agents.sh`

What would you like to do today?"""

    def simulate_ai_response(self, user_input: str) -> str:
        """Simulate AI response for demo purposes"""
        user_input_lower = user_input.lower()
        
        if "balance" in user_input_lower or "account" in user_input_lower:
            return """Here are your current account balances:

• **My Checking** (CHK-********): $1,250.00
• **My Savings** (SAV-********): $5,000.00

Would you like to make a transfer or check something else?"""
        
        elif "transfer" in user_input_lower:
            if "100" in user_input and "checking" in user_input_lower and "savings" in user_input_lower:
                return """✅ **Transfer completed successfully!**

**Transaction Details:**
- Transaction ID: TXN-ABC********9
- From: My Checking (CHK-********)
- To: My Savings (SAV-********)
- Amount: $100.00
- Status: COMPLETED
- Date: """ + datetime.now().strftime("%Y-%m-%d %H:%M:%S") + """

Your new balances:
- My Checking: $1,150.00
- My Savings: $5,100.00

Is there anything else I can help you with?"""
            else:
                return """I'd be happy to help you with a transfer! 

To process your transfer, I need:
1. **From account** - Which account to transfer from?
2. **To account** - Which account to transfer to?
3. **Amount** - How much would you like to transfer?

Your available accounts:
• My Checking (CHK-********) - $1,250.00
• My Savings (SAV-********) - $5,000.00

Please let me know the details!"""
        
        elif "recurring" in user_input_lower or "schedule" in user_input_lower:
            return """Here are your recurring payments:

🟢 **Active Recurring Payments:**
• REC-PAY001: $200.00 monthly from Checking to Savings
  - Next payment: 2024-01-15
  - Status: Active

Would you like to:
- Create a new recurring payment
- Modify an existing payment
- Cancel a payment

Just let me know what you'd like to do!"""
        
        elif "transaction" in user_input_lower or "history" in user_input_lower:
            return """Here's your recent transaction history:

**🔄 IMMEDIATE TRANSFERS:**
✅ TXN-ABC********9 - $100.00 (Checking → Savings) - Today 2:30 PM
✅ TXN-DEF9******** - $50.00 (Savings → Checking) - Yesterday 10:15 AM
✅ TXN-GHI456789123 - $200.00 (Checking → Savings) - Dec 28, 2024

**🔁 RECURRING TRANSFERS:**
✅ TXN-REC001 - $200.00 (Checking → Savings) - Dec 15, 2024
✅ TXN-REC002 - $200.00 (Checking → Savings) - Nov 15, 2024

Would you like to see more details about any specific transaction?"""
        
        else:
            return """I'm here to help with your banking needs! I can assist you with:

• **Account Management** - Check balances, view account details
• **Money Transfers** - Immediate transfers between your accounts
• **Recurring Payments** - Set up, modify, or cancel scheduled transfers
• **Transaction History** - View your recent banking activity
• **Account Creation** - Open new checking or savings accounts

What would you like to do today?"""

    def render_transaction_dashboard(self):
        """Render transaction dashboard with charts"""
        st.header("📊 Transaction Dashboard")
        
        # Mock transaction data for visualization
        transaction_data = [
            {"date": "2024-01-01", "type": "Transfer", "amount": 100, "status": "Completed"},
            {"date": "2024-01-02", "type": "Transfer", "amount": 50, "status": "Completed"},
            {"date": "2024-01-03", "type": "Recurring", "amount": 200, "status": "Completed"},
            {"date": "2024-01-04", "type": "Transfer", "amount": 75, "status": "Completed"},
            {"date": "2024-01-05", "type": "Transfer", "amount": 25, "status": "Failed"},
        ]
        
        df = pd.DataFrame(transaction_data)
        df['date'] = pd.to_datetime(df['date'])
        
        col1, col2 = st.columns(2)
        
        with col1:
            # Transaction volume chart
            fig_volume = px.bar(
                df, 
                x='date', 
                y='amount', 
                color='type',
                title="Daily Transaction Volume",
                labels={'amount': 'Amount ($)', 'date': 'Date'}
            )
            st.plotly_chart(fig_volume, use_container_width=True)
        
        with col2:
            # Transaction status pie chart
            status_counts = df['status'].value_counts()
            fig_status = px.pie(
                values=status_counts.values,
                names=status_counts.index,
                title="Transaction Status Distribution"
            )
            st.plotly_chart(fig_status, use_container_width=True)

    def render_logs_section(self):
        """Render system logs for debugging"""
        with st.expander("🔍 System Logs & Debug Information"):
            st.subheader("Component Status")

            col1, col2, col3 = st.columns(3)

            with col1:
                st.success("**Spring Boot Backend**")
                st.text("Status: ✅ Running")
                st.text("Port: 8080")
                st.text("Database: H2 In-Memory")
                st.text("Auth: None Required")

            with col2:
                st.success("**MCP Server**")
                st.text("Status: ✅ Running")
                st.text("Tools: 9 Available")
                st.text("Protocol: MCP 1.0")

            with col3:
                st.success("**AI Agents**")
                st.text("Status: ✅ Ready")
                st.text("LLM: OpenAI GPT-4")
                st.text("Protocol: A2A WebSocket")

            st.subheader("Architecture Flow")
            st.markdown("""
            ```
            User Input (Natural Language)
                    ↓
            Streamlit Frontend (Port 8501)
                    ↓ HTTP
            Client Agent (Port 8766) - OpenAI LLM
                    ↓ WebSocket A2A
            Server Agent (Port 8765) - OpenAI LLM
                    ↓ MCP Protocol
            MCP Server (Tools)
                    ↓ REST API
            Spring Boot Backend (Port 8080)
                    ↓
            H2 Database (In-Memory)
            ```
            """)

            st.subheader("Recent System Activity")
            logs = [
                f"{datetime.now().strftime('%H:%M:%S')} - User: No login required - direct access",
                f"{datetime.now().strftime('%H:%M:%S')} - Client Agent: Processing natural language request",
                f"{datetime.now().strftime('%H:%M:%S')} - Server Agent: Analyzing intent with OpenAI GPT-4",
                f"{datetime.now().strftime('%H:%M:%S')} - MCP Server: Executing banking tool",
                f"{datetime.now().strftime('%H:%M:%S')} - Backend API: Processing transaction",
                f"{datetime.now().strftime('%H:%M:%S')} - Client Agent: Generating conversational response"
            ]

            for log in logs:
                st.text(log)

            st.subheader("Setup Instructions")
            st.markdown("""
            **To enable full AI functionality:**

            1. Set OpenAI API key:
            ```bash
            export OPENAI_API_KEY=your-key-here
            ```

            2. Start all services:
            ```bash
            ./scripts/start-all.sh
            ```

            3. Or start individually:
            ```bash
            ./scripts/start-backend.sh     # Terminal 1
            ./scripts/start-mcp-server.sh  # Terminal 2
            ./scripts/start-agents.sh      # Terminal 3
            ./scripts/start-frontend.sh    # Terminal 4
            ```
            """)

    def run(self):
        """Main application runner"""
        # Initialize conversation
        self.initialize_conversation()

        # Render components
        self.render_header()
        self.render_sidebar()

        # Main content tabs
        tab1, tab2, tab3 = st.tabs(["💬 Chat Assistant", "📊 Dashboard", "🔍 System Logs"])

        with tab1:
            self.render_chat_interface()

        with tab2:
            self.render_transaction_dashboard()

        with tab3:
            self.render_logs_section()

def main():
    """Main entry point"""
    app = StreamlitApp()
    app.run()

if __name__ == "__main__":
    main()
