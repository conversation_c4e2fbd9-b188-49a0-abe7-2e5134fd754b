#!/bin/bash

echo "🎨 Starting Streamlit Frontend..."

# Navigate to frontend directory
cd "$(dirname "$0")/../frontend"

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "Creating Python virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
source venv/bin/activate

# Install dependencies
echo "Installing dependencies..."
pip install -r requirements.txt

# Start Streamlit app
echo "✅ Starting Streamlit Frontend..."
streamlit run app.py --server.port 8501 --server.address 0.0.0.0
