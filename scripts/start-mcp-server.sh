#!/bin/bash

echo "🔧 Starting MCP Server..."

# Navigate to MCP server directory
cd "$(dirname "$0")/../mcp-server"

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "Creating Python virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
source venv/bin/activate

# Install dependencies
echo "Installing dependencies..."
pip install -r requirements.txt

# Start MCP server
echo "✅ Starting MCP Server..."
python server.py
