#!/bin/bash

echo "🚀 Starting Spring Boot Backend..."

# Create logs directory if it doesn't exist
mkdir -p logs

# Navigate to project root
cd "$(dirname "$0")/.."

# Build and run the Spring Boot application
echo "Building application..."
./gradlew build -x test

if [ $? -eq 0 ]; then
    echo "✅ Build successful. Starting application..."
    ./gradlew bootRun
else
    echo "❌ Build failed. Please check the errors above."
    exit 1
fi
