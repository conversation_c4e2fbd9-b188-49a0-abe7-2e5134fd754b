#!/bin/bash

echo "🧪 Testing Streamlit Frontend..."

# Navigate to frontend directory
cd "$(dirname "$0")/../frontend"

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "Creating Python virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
source venv/bin/activate

# Install dependencies
echo "Installing dependencies..."
pip install -r requirements.txt

# Test if Streamlit can start
echo "Testing Streamlit startup..."
echo "Starting Streamlit on port 8501..."
echo "Open your browser to: http://localhost:8501"
echo "Press Ctrl+C to stop"

streamlit run app.py --server.port 8501 --server.address 0.0.0.0
