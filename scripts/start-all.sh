#!/bin/bash

echo "🚀 Starting Intelligent Money Transfer System..."
echo "================================================"

# Function to cleanup background processes
cleanup() {
    echo "🛑 Shutting down all services..."
    kill $(jobs -p) 2>/dev/null
    exit 0
}

# Set trap to cleanup on script exit
trap cleanup SIGINT SIGTERM

# Create logs directory
mkdir -p logs

# Start backend
echo "1️⃣ Starting Spring Boot Backend..."
./scripts/start-backend.sh > logs/backend.log 2>&1 &
BACKEND_PID=$!

# Wait for backend to start
echo "⏳ Waiting for backend to start..."
sleep 15

# Check if backend is running
if curl -s http://localhost:8080/actuator/health > /dev/null; then
    echo "✅ Backend is running"
else
    echo "❌ Backend failed to start. Check logs/backend.log"
    exit 1
fi

# Start MCP server
echo "2️⃣ Starting MCP Server..."
./scripts/start-mcp-server.sh > logs/mcp-server.log 2>&1 &
MCP_PID=$!

# Wait for MCP server to start
sleep 5

# Start AI agents
echo "3️⃣ Starting AI Agents..."
./scripts/start-agents.sh > logs/agents.log 2>&1 &
AGENTS_PID=$!

# Wait for agents to start
sleep 5

# Start frontend
echo "4️⃣ Starting Streamlit Frontend..."
./scripts/start-frontend.sh > logs/frontend.log 2>&1 &
FRONTEND_PID=$!

# Wait for frontend to start
sleep 10

echo ""
echo "🎉 All services started successfully!"
echo "================================================"
echo "📊 Backend API:      http://localhost:8080"
echo "🎨 Frontend:         http://localhost:8501"
echo "🔍 H2 Console:       http://localhost:8080/h2-console"
echo "📝 Logs directory:   ./logs/"
echo ""
echo "Press Ctrl+C to stop all services"
echo "================================================"

# Keep script running and show status
while true; do
    sleep 30
    echo "$(date): System running - Backend: $BACKEND_PID, MCP: $MCP_PID, Agents: $AGENTS_PID, Frontend: $FRONTEND_PID"
done
