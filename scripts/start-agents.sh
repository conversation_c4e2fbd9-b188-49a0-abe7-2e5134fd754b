#!/bin/bash

echo "🤖 Starting AI Agents..."

# Navigate to agents directory
cd "$(dirname "$0")/../agents"

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "Creating Python virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
source venv/bin/activate

# Install dependencies
echo "Installing dependencies..."
pip install -r requirements.txt

# Check if OpenAI API key is set
if [ -z "$OPENAI_API_KEY" ]; then
    echo "⚠️  Warning: OPENAI_API_KEY environment variable is not set."
    echo "Please set it before starting the agents:"
    echo "export OPENAI_API_KEY=your_api_key_here"
    echo ""
fi

# Start server agent in background
echo "Starting Server Agent (WebSocket server on port 8765)..."
python server_agent.py &
SERVER_AGENT_PID=$!

# Wait for server agent to start
echo "Waiting for Server Agent to initialize..."
sleep 5

# Start client agent (HTTP server on port 8766)
echo "✅ Starting Client Agent (HTTP server on port 8766)..."
python client_agent.py &
CLIENT_AGENT_PID=$!

echo ""
echo "🎉 AI Agents started successfully!"
echo "📡 Server Agent (WebSocket): ws://localhost:8765"
echo "🌐 Client Agent (HTTP): http://localhost:8766"
echo "Server Agent PID: $SERVER_AGENT_PID"
echo "Client Agent PID: $CLIENT_AGENT_PID"
echo ""
echo "Press Ctrl+C to stop all agents"

# Function to cleanup on exit
cleanup() {
    echo "🛑 Stopping AI Agents..."
    kill $SERVER_AGENT_PID $CLIENT_AGENT_PID 2>/dev/null
    exit 0
}

# Set trap for cleanup
trap cleanup SIGINT SIGTERM

# Keep script running
wait
