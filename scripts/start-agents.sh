#!/bin/bash

echo "🤖 Starting AI Agents..."

# Navigate to agents directory
cd "$(dirname "$0")/../agents"

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "Creating Python virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
source venv/bin/activate

# Install dependencies
echo "Installing dependencies..."
pip install -r requirements.txt

# Start server agent in background
echo "Starting Server Agent..."
python server_agent.py &
SERVER_AGENT_PID=$!

# Wait a moment for server agent to start
sleep 3

# Start client agent
echo "✅ Starting Client Agent..."
python client_agent.py &
CLIENT_AGENT_PID=$!

echo "AI Agents started successfully!"
echo "Server Agent PID: $SERVER_AGENT_PID"
echo "Client Agent PID: $CLIENT_AGENT_PID"

# Keep script running
wait
