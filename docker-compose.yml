version: '3.8'

services:
  # Spring Boot Backend
  backend:
    build:
      context: .
      dockerfile: Dockerfile.backend
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - SPRING_DATASOURCE_URL=jdbc:h2:mem:transferdb
    volumes:
      - ./logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - transfer-network

  # MCP Server
  mcp-server:
    build:
      context: ./mcp-server
      dockerfile: Dockerfile
    environment:
      - BACKEND_BASE_URL=http://backend:8080/api
    depends_on:
      backend:
        condition: service_healthy
    volumes:
      - ./logs:/app/logs
    networks:
      - transfer-network

  # AI Agents
  agents:
    build:
      context: ./agents
      dockerfile: Dockerfile
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - GOOGLE_CLOUD_PROJECT_ID=${GOOG<PERSON>_CLOUD_PROJECT_ID}
      - MCP_SERVER_URL=http://mcp-server:8765
    depends_on:
      - mcp-server
    volumes:
      - ./logs:/app/logs
    networks:
      - transfer-network

  # Streamlit Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "8501:8501"
    environment:
      - AGENTS_URL=http://agents:8766
    depends_on:
      - agents
    volumes:
      - ./logs:/app/logs
    networks:
      - transfer-network

networks:
  transfer-network:
    driver: bridge

volumes:
  logs:
    driver: local
