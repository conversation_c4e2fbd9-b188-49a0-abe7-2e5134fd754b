# 🔧 Troubleshooting Guide

## 🚨 Common Issues and Solutions

### 1. Gradle Build Failures

#### Issue: "Could not create task ':test'" or "Type T not present"
```
FAILURE: Build failed with an exception.
* What went wrong:
Could not determine the dependencies of task ':check'.
> Could not create task ':test'.
```

**Solution:**
The build.gradle.kts has been configured to disable problematic test tasks. Use these commands:

```bash
# Instead of ./gradlew build
./gradlew clean compileJava bootJar

# Or to run the application
./gradlew bootRun
```

#### Issue: Java Version Compatibility
```bash
# Check your Java version
java -version

# Should show Java 17 or higher
# If not, install Java 17:

# macOS (using Homebrew)
brew install openjdk@17

# Ubuntu/Debian
sudo apt install openjdk-17-jdk

# Set JAVA_HOME if needed
export JAVA_HOME=/path/to/java17
```

### 2. Port Conflicts

#### Issue: "Port already in use"
```bash
# Check what's using the ports
lsof -i :8080  # Backend
lsof -i :8501  # Frontend
lsof -i :8765  # Server Agent
lsof -i :8766  # Client Agent

# Kill processes if needed
kill -9 <PID>

# Or kill all Java processes
pkill -f java

# Or kill all Python processes
pkill -f python
```

### 3. OpenAI API Key Issues

#### Issue: "OPENAI_API_KEY not set"
```bash
# Set the environment variable
export OPENAI_API_KEY=sk-your-actual-api-key-here

# Verify it's set
echo $OPENAI_API_KEY

# Make it persistent (add to ~/.bashrc or ~/.zshrc)
echo 'export OPENAI_API_KEY=sk-your-actual-api-key-here' >> ~/.bashrc
source ~/.bashrc
```

#### Issue: Invalid API Key
- Verify your API key at: https://platform.openai.com/api-keys
- Make sure you have sufficient credits
- Check for any typos in the key

### 4. Python Dependencies

#### Issue: Module import errors
```bash
# Navigate to the component directory and recreate virtual environment
cd mcp-server
rm -rf venv
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt

# Repeat for agents and frontend directories
cd ../agents
rm -rf venv
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt

cd ../frontend
rm -rf venv
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```

### 5. Service Connection Issues

#### Issue: "Cannot connect to Client Agent"
```bash
# Check if Client Agent is running
curl http://localhost:8766/docs

# If not running, start it manually
cd agents
source venv/bin/activate
python client_agent.py
```

#### Issue: "Cannot connect to Server Agent"
```bash
# Check if Server Agent WebSocket is running
# You should see it listening on port 8765
netstat -an | grep 8765

# If not running, start it manually
cd agents
source venv/bin/activate
python server_agent.py
```

#### Issue: "Cannot connect to Backend"
```bash
# Check backend health
curl http://localhost:8080/actuator/health

# If not responding, check logs
tail -f logs/backend.log

# Start backend manually
./gradlew bootRun
```

### 6. Database Issues

#### Issue: H2 Console not accessible
```bash
# The H2 console should be available at:
# http://localhost:8080/h2-console

# Use these connection settings:
# JDBC URL: jdbc:h2:mem:transferdb
# User Name: sa
# Password: password
```

### 7. Frontend Issues

#### Issue: Chat messages are hard to read (dark text on dark background)
**Fixed!** The latest version uses Streamlit's built-in chat components for better readability.

```bash
# Update to latest version and restart frontend
./scripts/start-frontend.sh
```

#### Issue: Streamlit not starting
```bash
# Check if Streamlit is installed
cd frontend
source venv/bin/activate
pip list | grep streamlit

# If not installed
pip install streamlit

# Test frontend only
./scripts/test-frontend.sh

# Start manually
streamlit run app.py --server.port 8501
```

#### Issue: "Cannot connect to banking system"
This is normal when AI agents are not running. The frontend will work in demo mode.

```bash
# Check Client Agent status (optional)
curl http://localhost:8766/docs

# For full AI functionality, start the agents
./scripts/start-agents.sh
```

### 8. Memory Issues

#### Issue: Out of memory errors
```bash
# Increase JVM memory for Gradle
export GRADLE_OPTS="-Xmx2g -XX:MaxMetaspaceSize=512m"

# For the Spring Boot application
export JAVA_OPTS="-Xmx1g"
```

### 9. Network Issues

#### Issue: Services can't communicate
```bash
# Check if all services are running on correct ports
netstat -an | grep -E "(8080|8501|8765|8766)"

# Should show:
# *.8080 (Backend)
# *.8501 (Frontend) 
# *.8765 (Server Agent)
# *.8766 (Client Agent)
```

## 🔍 Debugging Steps

### 1. Check Service Status
```bash
# Backend
curl http://localhost:8080/actuator/health

# Client Agent
curl http://localhost:8766/docs

# Frontend
curl http://localhost:8501

# Check processes
ps aux | grep -E "(java|python|streamlit)"
```

### 2. View Logs
```bash
# All logs are in the logs/ directory
tail -f logs/backend.log
tail -f logs/agents.log
tail -f logs/mcp-server.log
tail -f logs/frontend.log

# Or view all logs together
tail -f logs/*.log
```

### 3. Test Individual Components
```bash
# Test backend compilation
./scripts/test-backend.sh

# Test MCP server
cd mcp-server
source venv/bin/activate
python server.py

# Test agents
cd agents
source venv/bin/activate
python server_agent.py  # In one terminal
python client_agent.py  # In another terminal
```

### 4. Clean Restart
```bash
# Kill all processes
pkill -f java
pkill -f python
pkill -f streamlit

# Clean build
./gradlew clean

# Remove Python virtual environments
rm -rf */venv

# Start fresh
./scripts/start-all.sh
```

## 🆘 Getting Help

If you're still having issues:

1. **Check the logs** in the `logs/` directory
2. **Verify all prerequisites** are installed (Java 17+, Python 3.8+)
3. **Ensure OpenAI API key** is set and valid
4. **Try starting services individually** to isolate the problem
5. **Check port availability** and kill conflicting processes

## 📞 Support Checklist

When reporting issues, please provide:

- [ ] Operating system and version
- [ ] Java version (`java -version`)
- [ ] Python version (`python3 --version`)
- [ ] Error messages from logs
- [ ] Which services are running (`netstat -an | grep -E "(8080|8501|8765|8766)"`)
- [ ] OpenAI API key status (set but don't share the actual key)

---

**💡 Most issues are resolved by ensuring the OpenAI API key is set and all services start in the correct order.**
